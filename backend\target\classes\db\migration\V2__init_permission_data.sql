-- 动态权限管理系统 - 基础数据初始化脚本
-- 版本: 2.0
-- 更新时间: 2024-12-19

-- 清空现有数据（开发环境）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE user_roles;
TRUNCATE TABLE role_permissions;
TRUNCATE TABLE user_permissions;
TRUNCATE TABLE permission_policies;
TRUNCATE TABLE permission_logs;
TRUNCATE TABLE permissions;
TRUNCATE TABLE roles;
TRUNCATE TABLE users;
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 1. 插入系统内置角色
-- ========================================
INSERT INTO roles (id, name, code, description, is_system, sort_order) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', TRUE, 1),
(2, '系统管理员', 'SYSTEM_ADMIN', '系统管理员，负责系统配置和用户管理', TRUE, 2),
(3, '普通用户', 'USER', '普通用户，基础权限', TRUE, 3);

-- ========================================
-- 2. 插入完整的权限体系
-- ========================================

-- 2.1 用户管理模块权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
-- 用户管理主模块
(1, '用户管理', 'USER_MANAGEMENT', '用户管理模块', 'MENU', '/admin/users', 'ALL', NULL, 1, 1, TRUE),
-- 用户管理子权限
(2, '查看用户列表', 'USER_LIST', '查看用户列表', 'API', '/api/users', 'GET', 1, 2, 1, TRUE),
(3, '创建用户', 'USER_CREATE', '创建新用户', 'API', '/api/users', 'POST', 1, 2, 2, TRUE),
(4, '编辑用户', 'USER_UPDATE', '编辑用户信息', 'API', '/api/users/*', 'PUT', 1, 2, 3, TRUE),
(5, '删除用户', 'USER_DELETE', '删除用户', 'API', '/api/users/*', 'DELETE', 1, 2, 4, TRUE),
(6, '重置用户密码', 'USER_RESET_PASSWORD', '重置用户密码', 'API', '/api/users/*/reset-password', 'POST', 1, 2, 5, TRUE),
(7, '用户状态管理', 'USER_STATUS_MANAGE', '管理用户状态（启用/禁用/锁定）', 'API', '/api/users/*/status', 'PATCH', 1, 2, 6, TRUE),
(8, '用户角色分配', 'USER_ROLE_ASSIGN', '为用户分配角色', 'API', '/api/users/*/roles', 'POST', 1, 2, 7, TRUE),

-- 2.2 角色管理模块权限
(10, '角色管理', 'ROLE_MANAGEMENT', '角色管理模块', 'MENU', '/admin/roles', 'ALL', NULL, 1, 2, TRUE),
-- 角色管理子权限
(11, '查看角色列表', 'ROLE_LIST', '查看角色列表', 'API', '/api/roles', 'GET', 10, 2, 1, TRUE),
(12, '创建角色', 'ROLE_CREATE', '创建新角色', 'API', '/api/roles', 'POST', 10, 2, 2, TRUE),
(13, '编辑角色', 'ROLE_UPDATE', '编辑角色信息', 'API', '/api/roles/*', 'PUT', 10, 2, 3, TRUE),
(14, '删除角色', 'ROLE_DELETE', '删除角色', 'API', '/api/roles/*', 'DELETE', 10, 2, 4, TRUE),
(15, '分配角色权限', 'ROLE_ASSIGN_PERMISSIONS', '为角色分配权限', 'API', '/api/roles/*/permissions', 'POST', 10, 2, 5, TRUE),
(16, '角色用户管理', 'ROLE_USER_MANAGE', '管理角色下的用户', 'API', '/api/roles/*/users', 'POST', 10, 2, 6, TRUE),

-- 2.3 权限管理模块权限
(20, '权限管理', 'PERMISSION_MANAGEMENT', '权限管理模块', 'MENU', '/admin/permissions', 'ALL', NULL, 1, 3, TRUE),
-- 权限管理子权限
(21, '查看权限列表', 'PERMISSION_LIST', '查看权限列表', 'API', '/api/permissions', 'GET', 20, 2, 1, TRUE),
(22, '创建权限', 'PERMISSION_CREATE', '创建新权限', 'API', '/api/permissions', 'POST', 20, 2, 2, TRUE),
(23, '编辑权限', 'PERMISSION_UPDATE', '编辑权限信息', 'API', '/api/permissions/*', 'PUT', 20, 2, 3, TRUE),
(24, '删除权限', 'PERMISSION_DELETE', '删除权限', 'API', '/api/permissions/*', 'DELETE', 20, 2, 4, TRUE),
(25, '权限树查看', 'PERMISSION_TREE', '查看权限树形结构', 'API', '/api/permissions/tree', 'GET', 20, 2, 5, TRUE),

-- 2.4 系统监控权限
(30, '系统监控', 'SYSTEM_MONITOR', '系统监控模块', 'MENU', '/admin/monitor', 'ALL', NULL, 1, 4, TRUE),
-- 系统监控子权限
(31, '查看系统日志', 'LOG_VIEW', '查看系统操作日志', 'API', '/api/logs', 'GET', 30, 2, 1, TRUE),
(32, '在线用户管理', 'ONLINE_USERS', '查看和管理在线用户', 'API', '/api/online-users', 'GET', 30, 2, 2, TRUE),
(33, '系统健康检查', 'HEALTH_CHECK', '查看系统健康状态', 'API', '/api/health', 'GET', 30, 2, 3, TRUE),

-- 2.5 个人中心权限
(40, '个人中心', 'PROFILE', '个人中心模块', 'MENU', '/profile', 'ALL', NULL, 1, 5, TRUE),
-- 个人中心子权限
(41, '查看个人信息', 'PROFILE_VIEW', '查看个人信息', 'API', '/api/profile', 'GET', 40, 2, 1, TRUE),
(42, '修改个人信息', 'PROFILE_UPDATE', '修改个人信息', 'API', '/api/profile', 'PUT', 40, 2, 2, TRUE),
(43, '修改密码', 'PASSWORD_CHANGE', '修改个人密码', 'API', '/api/profile/password', 'PUT', 40, 2, 3, TRUE),

-- 2.6 仪表盘权限
(50, '仪表盘', 'DASHBOARD', '仪表盘模块', 'MENU', '/dashboard', 'ALL', NULL, 1, 6, TRUE),
-- 仪表盘子权限
(51, '查看统计数据', 'DASHBOARD_STATS', '查看仪表盘统计数据', 'API', '/api/dashboard/stats', 'GET', 50, 2, 1, TRUE),
(52, '查看系统状态', 'DASHBOARD_SYSTEM_STATUS', '查看系统运行状态', 'API', '/api/dashboard/system-status', 'GET', 50, 2, 2, TRUE),

-- 2.7 认证相关权限（通常所有用户都有）
(60, '认证管理', 'AUTH_MANAGEMENT', '认证管理模块', 'API', '/api/auth', 'ALL', NULL, 1, 7, TRUE),
-- 认证子权限
(61, '用户登录', 'AUTH_LOGIN', '用户登录', 'API', '/api/auth/login', 'POST', 60, 2, 1, TRUE),
(62, '用户注册', 'AUTH_REGISTER', '用户注册', 'API', '/api/auth/register', 'POST', 60, 2, 2, TRUE),
(63, '用户登出', 'AUTH_LOGOUT', '用户登出', 'API', '/api/auth/logout', 'POST', 60, 2, 3, TRUE),
(64, '获取用户信息', 'AUTH_ME', '获取当前用户信息', 'API', '/api/auth/me', 'GET', 60, 2, 4, TRUE),

-- 2.8 按钮级权限（前端UI控制）
(70, '用户管理按钮', 'USER_BUTTONS', '用户管理页面按钮权限', 'BUTTON', NULL, NULL, 1, 3, 1, TRUE),
(71, '创建用户按钮', 'USER_CREATE_BUTTON', '创建用户按钮', 'BUTTON', NULL, NULL, 70, 3, 1, TRUE),
(72, '编辑用户按钮', 'USER_EDIT_BUTTON', '编辑用户按钮', 'BUTTON', NULL, NULL, 70, 3, 2, TRUE),
(73, '删除用户按钮', 'USER_DELETE_BUTTON', '删除用户按钮', 'BUTTON', NULL, NULL, 70, 3, 3, TRUE),

(80, '角色管理按钮', 'ROLE_BUTTONS', '角色管理页面按钮权限', 'BUTTON', NULL, NULL, 10, 3, 1, TRUE),
(81, '创建角色按钮', 'ROLE_CREATE_BUTTON', '创建角色按钮', 'BUTTON', NULL, NULL, 80, 3, 1, TRUE),
(82, '编辑角色按钮', 'ROLE_EDIT_BUTTON', '编辑角色按钮', 'BUTTON', NULL, NULL, 80, 3, 2, TRUE),
(83, '删除角色按钮', 'ROLE_DELETE_BUTTON', '删除角色按钮', 'BUTTON', NULL, NULL, 80, 3, 3, TRUE),

(90, '权限管理按钮', 'PERMISSION_BUTTONS', '权限管理页面按钮权限', 'BUTTON', NULL, NULL, 20, 3, 1, TRUE),
(91, '创建权限按钮', 'PERMISSION_CREATE_BUTTON', '创建权限按钮', 'BUTTON', NULL, NULL, 90, 3, 1, TRUE),
(92, '编辑权限按钮', 'PERMISSION_EDIT_BUTTON', '编辑权限按钮', 'BUTTON', NULL, NULL, 90, 3, 2, TRUE),
(93, '删除权限按钮', 'PERMISSION_DELETE_BUTTON', '删除权限按钮', 'BUTTON', NULL, NULL, 90, 3, 3, TRUE);

-- ========================================
-- 3. 角色权限分配
-- ========================================

-- 3.1 为超级管理员角色分配所有权限
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 1, p.id, 1 FROM permissions p;

-- 3.2 为系统管理员分配管理权限（除了超级管理员专有权限）
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 2, p.id, 1
FROM permissions p
WHERE p.code IN (
    -- 用户管理权限
    'USER_MANAGEMENT', 'USER_LIST', 'USER_CREATE', 'USER_UPDATE', 'USER_RESET_PASSWORD', 'USER_STATUS_MANAGE', 'USER_ROLE_ASSIGN',
    'USER_BUTTONS', 'USER_CREATE_BUTTON', 'USER_EDIT_BUTTON',

    -- 角色管理权限（不包括删除系统角色）
    'ROLE_MANAGEMENT', 'ROLE_LIST', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_ASSIGN_PERMISSIONS', 'ROLE_USER_MANAGE',
    'ROLE_BUTTONS', 'ROLE_CREATE_BUTTON', 'ROLE_EDIT_BUTTON',

    -- 权限管理权限（只读）
    'PERMISSION_MANAGEMENT', 'PERMISSION_LIST', 'PERMISSION_TREE',

    -- 系统监控权限
    'SYSTEM_MONITOR', 'LOG_VIEW', 'ONLINE_USERS', 'HEALTH_CHECK',

    -- 仪表盘权限
    'DASHBOARD', 'DASHBOARD_STATS', 'DASHBOARD_SYSTEM_STATUS',

    -- 个人中心权限
    'PROFILE', 'PROFILE_VIEW', 'PROFILE_UPDATE', 'PASSWORD_CHANGE',

    -- 认证权限
    'AUTH_MANAGEMENT', 'AUTH_LOGIN', 'AUTH_LOGOUT', 'AUTH_ME'
);

-- 3.3 为普通用户分配基础权限
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 3, p.id, 1
FROM permissions p
WHERE p.code IN (
    -- 仪表盘基础权限
    'DASHBOARD', 'DASHBOARD_STATS',

    -- 个人中心权限
    'PROFILE', 'PROFILE_VIEW', 'PROFILE_UPDATE', 'PASSWORD_CHANGE',

    -- 认证权限
    'AUTH_MANAGEMENT', 'AUTH_LOGIN', 'AUTH_REGISTER', 'AUTH_LOGOUT', 'AUTH_ME'
);

-- ========================================
-- 4. 创建默认用户
-- ========================================

-- 4.1 创建默认超级管理员用户
-- 密码：admin123 (BCrypt加密后的值)
INSERT INTO users (id, username, password, email, real_name, status, created_by) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKVjzieMwkOBSaEwHOsKchAg/XT6', '<EMAIL>', '系统管理员', 'ACTIVE', 1);

-- 4.2 创建示例系统管理员用户
-- 密码：sysadmin123
INSERT INTO users (id, username, password, email, real_name, status, created_by) VALUES
(2, 'sysadmin', '$2a$10$8K1p/wgZH.FcQiT4qY6OuOEaZQhcQQhQoQhQoQhQoQhQoQhQoQhQo', '<EMAIL>', '系统管理员', 'ACTIVE', 1);

-- 4.3 创建示例普通用户
-- 密码：user123
INSERT INTO users (id, username, password, email, real_name, status, created_by) VALUES
(3, 'user', '$2a$10$7J0o/vfYG.EbPiS3pX5NtNDaYPgbPPgbPPgbPPgbPPgbPPgbPPgbP', '<EMAIL>', '普通用户', 'ACTIVE', 1);

-- ========================================
-- 5. 用户角色分配
-- ========================================

-- 5.1 为默认管理员分配超级管理员角色
INSERT INTO user_roles (user_id, role_id, granted_by) VALUES (1, 1, 1);

-- 5.2 为系统管理员分配系统管理员角色
INSERT INTO user_roles (user_id, role_id, granted_by) VALUES (2, 2, 1);

-- 5.3 为普通用户分配普通用户角色
INSERT INTO user_roles (user_id, role_id, granted_by) VALUES (3, 3, 1);

-- ========================================
-- 6. 权限策略配置（动态权限规则）
-- ========================================

INSERT INTO permission_policies (name, description, resource_pattern, condition_expression, effect, priority, created_by) VALUES
-- 超级管理员策略
('超级管理员全权限策略', '超级管理员可以访问所有资源', '/**', 'hasRole(''SUPER_ADMIN'')', 'ALLOW', 1000, 1),

-- 系统管理员策略
('系统管理员API策略', '系统管理员可以访问管理API', '/api/admin/**', 'hasRole(''SYSTEM_ADMIN'')', 'ALLOW', 900, 1),

-- 用户数据访问策略
('用户数据访问策略', '用户只能访问自己的数据', '/api/users/{userId}/**', 'authentication.principal.id == #userId', 'ALLOW', 800, 1),

-- 工作时间访问策略
('工作时间访问策略', '只允许工作时间访问敏感API', '/api/sensitive/**', 'T(java.time.LocalTime).now().hour >= 9 && T(java.time.LocalTime).now().hour <= 18', 'ALLOW', 700, 1),

-- IP访问限制策略
('内网IP访问策略', '只允许内网IP访问管理功能', '/api/admin/**', 'request.remoteAddr.startsWith(''192.168.'') or request.remoteAddr.startsWith(''10.'')', 'ALLOW', 600, 1),

-- 角色权限策略
('角色管理权限策略', '只有超级管理员可以删除系统角色', '/api/roles/*/delete', 'hasRole(''SUPER_ADMIN'')', 'ALLOW', 500, 1);

-- ========================================
-- 7. 重置自增ID序列
-- ========================================

-- 重置表的自增ID，确保后续插入的数据ID从正确的值开始
ALTER TABLE users AUTO_INCREMENT = 100;
ALTER TABLE roles AUTO_INCREMENT = 100;
ALTER TABLE permissions AUTO_INCREMENT = 100;
ALTER TABLE user_roles AUTO_INCREMENT = 100;
ALTER TABLE role_permissions AUTO_INCREMENT = 100;
ALTER TABLE user_permissions AUTO_INCREMENT = 100;
ALTER TABLE permission_policies AUTO_INCREMENT = 100;
ALTER TABLE permission_logs AUTO_INCREMENT = 100;
