package zh.backend.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import zh.backend.annotation.RequirePermission;
import zh.backend.entity.Permission;
import zh.backend.entity.Role;
import zh.backend.service.RoleService;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/api/roles")
@RequirePermission("ROLE_MANAGEMENT")
public class RoleController {
    
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(RoleController.class);

    @Autowired
    private RoleService roleService;

    /**
     * 获取角色列表
     */
    @GetMapping
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<Page<Role>> getRoles(
            @RequestParam(name = "keyword", defaultValue = "") String keyword,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "sortBy", defaultValue = "sortOrder") String sortBy,
            @RequestParam(name = "sortDir", defaultValue = "asc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Role> roles = roleService.findByKeyword(keyword, pageable);
        return ResponseEntity.ok(roles);
    }

    /**
     * 获取所有激活的角色
     */
    @GetMapping("/active")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<List<Role>> getActiveRoles() {
        List<Role> roles = roleService.findActiveRoles();
        return ResponseEntity.ok(roles);
    }

    /**
     * 根据ID获取角色
     */
    @GetMapping("/{id}")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<Role> getRole(@PathVariable Long id) {
        Role role = roleService.findById(id);
        return ResponseEntity.ok(role);
    }

    /**
     * 创建角色
     */
    @PostMapping
    @RequirePermission("ROLE_CREATE")
    public ResponseEntity<Role> createRole(@Valid @RequestBody Role role) {
        Role createdRole = roleService.createRole(role);
        return ResponseEntity.ok(createdRole);
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    @RequirePermission("ROLE_UPDATE")
    public ResponseEntity<Role> updateRole(@PathVariable Long id, @Valid @RequestBody Role role) {
        Role updatedRole = roleService.updateRole(id, role);
        return ResponseEntity.ok(updatedRole);
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @RequirePermission("ROLE_DELETE")
    public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
        roleService.deleteRole(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/batch")
    @RequirePermission("ROLE_DELETE")
    public ResponseEntity<Void> deleteRoles(@RequestBody List<Long> ids) {
        roleService.deleteRoles(ids);
        return ResponseEntity.ok().build();
    }

    /**
     * 为角色分配权限
     */
    @PostMapping("/{id}/permissions")
    @RequirePermission("ROLE_ASSIGN_PERMISSIONS")
    public ResponseEntity<Void> assignPermissions(
            @PathVariable Long id, 
            @RequestBody List<Long> permissionIds) {
        roleService.assignPermissionsToRole(id, permissionIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 从角色移除权限
     */
    @DeleteMapping("/{id}/permissions")
    @RequirePermission("ROLE_ASSIGN_PERMISSIONS")
    public ResponseEntity<Void> removePermissions(
            @PathVariable Long id, 
            @RequestBody List<Long> permissionIds) {
        roleService.removePermissionsFromRole(id, permissionIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取角色的权限
     */
    @GetMapping("/{id}/permissions")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<List<Permission>> getRolePermissions(@PathVariable Long id) {
        List<Permission> permissions = roleService.getRolePermissions(id);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 复制角色权限
     */
    @PostMapping("/{sourceId}/copy-to/{targetId}")
    @RequirePermission("ROLE_ASSIGN_PERMISSIONS")
    public ResponseEntity<Void> copyRolePermissions(
            @PathVariable Long sourceId, 
            @PathVariable Long targetId) {
        roleService.copyRolePermissions(sourceId, targetId);
        return ResponseEntity.ok().build();
    }

    /**
     * 检查角色编码是否存在
     */
    @GetMapping("/exists/code")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<Boolean> checkCodeExists(@RequestParam String code) {
        boolean exists = roleService.existsByCode(code);
        return ResponseEntity.ok(exists);
    }

    /**
     * 检查角色名称是否存在
     */
    @GetMapping("/exists/name")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<Boolean> checkNameExists(@RequestParam String name) {
        boolean exists = roleService.existsByName(name);
        return ResponseEntity.ok(exists);
    }

    /**
     * 获取角色统计信息
     */
    @GetMapping("/statistics")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<RoleService.RoleStatistics> getRoleStatistics() {
        log.debug("Entering getRoleStatistics method...");
        try {
            RoleService.RoleStatistics statistics = roleService.getRoleStatistics();
            log.debug("Successfully retrieved role statistics.");
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("Error retrieving role statistics", e);
            // 重新抛出异常，让Spring默认处理（或后续添加到ControllerAdvice处理）
            // 注意：这里不应该直接返回 403，除非明确知道是权限问题
            throw e;
        }
    }
}
