package zh.backend.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger/OpenAPI 配置
 */
@Configuration
public class SwaggerConfig {

    @Value("${server.port:8080}")
    private String serverPort;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                    new Server().url("http://localhost:" + serverPort).description("本地开发环境"),
                    new Server().url("https://api.example.com").description("生产环境")
                ))
                .components(new Components()
                    .addSecuritySchemes("bearerAuth", 
                        new SecurityScheme()
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT")
                            .description("JWT认证令牌")
                    )
                )
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
    }

    private Info apiInfo() {
        return new Info()
                .title("动态权限管理系统 API")
                .description("""
                    ## 系统概述
                    
                    这是一个基于Spring Boot 3.0和Java 17开发的动态权限管理系统，提供完整的RBAC（基于角色的访问控制）功能。
                    
                    ## 主要功能
                    
                    - **用户管理**: 用户的增删改查、状态管理、密码重置等
                    - **角色管理**: 角色的创建、编辑、删除、权限分配等
                    - **权限管理**: 权限的层级管理、动态分配、缓存机制等
                    - **认证授权**: JWT令牌认证、权限验证、角色检查等
                    
                    ## 认证说明
                    
                    大部分API需要JWT令牌认证，请先调用登录接口获取token，然后在请求头中添加：
                    ```
                    Authorization: Bearer <your-jwt-token>
                    ```
                    
                    ## 权限说明
                    
                    系统采用细粒度权限控制，每个API都有对应的权限要求。权限编码格式为：`模块_操作`，例如：
                    - `USER_LIST`: 查看用户列表
                    - `USER_CREATE`: 创建用户
                    - `PERMISSION_UPDATE`: 更新权限
                    
                    ## 错误码说明
                    
                    - `200`: 操作成功
                    - `400`: 请求参数错误
                    - `401`: 未认证或令牌无效
                    - `403`: 权限不足
                    - `404`: 资源不存在
                    - `500`: 服务器内部错误
                    """)
                .version("1.0.0")
                .contact(new Contact()
                    .name("开发团队")
                    .email("<EMAIL>")
                    .url("https://github.com/example/permission-system"))
                .license(new License()
                    .name("MIT License")
                    .url("https://opensource.org/licenses/MIT"));
    }
}
