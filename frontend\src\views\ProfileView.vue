<template>
  <div class="profile-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><User /></el-icon>
            个人资料
          </h1>
          <p class="page-description">管理您的个人信息和账户设置</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="handleEditProfile" :icon="Edit" size="default">
            编辑资料
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户信息概览 -->
    <div class="user-overview">
      <el-card class="overview-card" shadow="never">
        <div class="user-info">
          <div class="avatar-section">
            <el-avatar :size="80" :icon="UserFilled" class="user-avatar" />
            <div class="avatar-actions">
              <el-button size="small" type="primary" link :icon="Camera">更换头像</el-button>
            </div>
          </div>
          <div class="info-section">
            <div class="user-details">
              <h2 class="user-name">{{ user?.realName || user?.username }}</h2>
              <p class="user-subtitle">{{ user?.email || '未设置邮箱' }}</p>
              <div class="user-roles">
                <el-tag v-for="role in user?.roles" :key="role" type="primary" size="small" effect="light" class="role-tag">
                  <el-icon class="tag-icon"><UserFilled /></el-icon>
                  {{ formatRoleName(role) }}
                </el-tag>
                <el-tag v-if="!user?.roles || user.roles.length === 0" type="info" size="small" effect="light">
                  普通用户
                </el-tag>
              </div>
            </div>
          </div>
          <div class="status-section">
            <div class="status-items">
              <div class="status-item">
                <div class="status-label">账户状态</div>
                <el-tag type="success" effect="plain" size="small">
                  <el-icon><CircleCheck /></el-icon>
                  活跃
                </el-tag>
              </div>
              <div class="status-item">
                <div class="status-label">邮箱验证</div>
                <el-tag type="success" effect="plain" size="small">
                  <el-icon><CircleCheck /></el-icon>
                  已验证
                </el-tag>
              </div>
              <div class="status-item">
                <div class="status-label">最后登录</div>
                <span class="status-value">刚刚</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-card class="content-card" shadow="never">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <el-tab-pane label="基本信息" name="info">
            <div class="tab-content">
              <div class="form-section">
                <h4 class="section-title">个人信息</h4>
                <el-form 
                  ref="profileFormRef" 
                  :model="profileForm" 
                  :rules="profileFormRules"
                  label-width="100px" 
                  @submit.prevent="updateProfile"
                  class="profile-form"
                >
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="用户名" prop="username">
                        <el-input
                          v-model="profileForm.username"
                          disabled
                          class="form-input"
                        >
                          <template #prefix>
                            <el-icon><User /></el-icon>
                          </template>
                        </el-input>
                        <div class="form-tip">
                          <el-icon><InfoFilled /></el-icon>
                          <span>用户名创建后不可修改</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="真实姓名" prop="realName">
                        <el-input
                          v-model="profileForm.realName"
                          placeholder="请输入您的真实姓名"
                          class="form-input"
                        >
                          <template #prefix>
                            <el-icon><UserFilled /></el-icon>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-form-item label="邮箱地址" prop="email">
                    <el-input
                      v-model="profileForm.email"
                      type="email"
                      placeholder="请输入您的邮箱地址"
                      class="form-input"
                    >
                      <template #prefix>
                        <el-icon><Message /></el-icon>
                      </template>
                    </el-input>
                    <div class="form-tip">
                      <el-icon><InfoFilled /></el-icon>
                      <span>邮箱地址用于接收重要通知和密码重置</span>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="updateProfile" :loading="loading" size="default">
                      <el-icon><Check /></el-icon>
                      {{ loading ? '保存中...' : '保存更改' }}
                    </el-button>
                    <el-button @click="resetProfileForm" size="default">
                      <el-icon><Refresh /></el-icon>
                      重置
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="安全设置" name="security">
            <div class="tab-content">
              <div class="form-section">
                <h4 class="section-title">修改密码</h4>
                <el-form 
                  ref="passwordFormRef" 
                  :model="passwordForm" 
                  :rules="passwordFormRules" 
                  label-width="100px" 
                  @submit.prevent="changePassword"
                  class="password-form"
                >
                  <el-form-item label="当前密码" prop="currentPassword">
                    <el-input
                      v-model="passwordForm.currentPassword"
                      type="password"
                      show-password
                      placeholder="请输入当前密码"
                      class="form-input"
                    >
                      <template #prefix>
                        <el-icon><Lock /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="新密码" prop="newPassword">
                        <el-input
                          v-model="passwordForm.newPassword"
                          type="password"
                          show-password
                          placeholder="请输入新密码"
                          class="form-input"
                        >
                          <template #prefix>
                            <el-icon><Key /></el-icon>
                          </template>
                        </el-input>
                        <div class="form-tip">
                          <el-icon><InfoFilled /></el-icon>
                          <span>密码长度至少6个字符，建议包含字母和数字</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="确认密码" prop="confirmPassword">
                        <el-input
                          v-model="passwordForm.confirmPassword"
                          type="password"
                          show-password
                          placeholder="请再次输入新密码"
                          class="form-input"
                        >
                          <template #prefix>
                            <el-icon><Key /></el-icon>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-form-item>
                    <el-button type="danger" @click="changePassword" :loading="loading" size="default">
                      <el-icon><Lock /></el-icon>
                      {{ loading ? '修改中...' : '修改密码' }}
                    </el-button>
                    <el-button @click="resetPasswordForm" size="default">
                      <el-icon><Refresh /></el-icon>
                      重置
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>

              <div class="form-section">
                <h4 class="section-title">安全提示</h4>
                <div class="security-tips">
                  <div class="tip-item">
                    <el-icon class="tip-icon"><Warning /></el-icon>
                    <div class="tip-content">
                      <div class="tip-title">定期更换密码</div>
                      <div class="tip-desc">建议每3-6个月更换一次密码，确保账户安全</div>
                    </div>
                  </div>
                  <div class="tip-item">
                    <el-icon class="tip-icon"><Document /></el-icon>
                    <div class="tip-content">
                      <div class="tip-title">使用强密码</div>
                      <div class="tip-desc">密码应包含大小写字母、数字和特殊字符的组合</div>
                    </div>
                  </div>
                  <div class="tip-item">
                    <el-icon class="tip-icon"><View /></el-icon>
                    <div class="tip-content">
                      <div class="tip-title">保护隐私</div>
                      <div class="tip-desc">不要在公共场所或不安全的网络环境下修改密码</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="权限详情" name="permissions">
            <div class="tab-content">
              <div class="permissions-overview">
                <div class="form-section">
                  <h4 class="section-title">我的角色</h4>
                  <div class="roles-display">
                    <div v-if="user?.roles && user.roles.length > 0" class="roles-list">
                      <div v-for="role in user.roles" :key="role" class="role-item">
                        <el-tag type="primary" effect="light" size="large" class="role-tag-large">
                          <el-icon class="tag-icon"><UserFilled /></el-icon>
                          <div class="tag-content">
                            <span class="tag-name">{{ formatRoleName(role) }}</span>
                            <span class="tag-desc">{{ getRoleDescription(role) }}</span>
                          </div>
                        </el-tag>
                      </div>
                    </div>
                    <div v-else class="no-roles">
                      <el-empty description="您当前没有特殊角色" :image-size="80">
                        <template #description>
                          <p>您拥有基本用户权限</p>
                        </template>
                      </el-empty>
                    </div>
                  </div>
                </div>

                <div class="form-section">
                  <h4 class="section-title">我的权限</h4>
                  <div class="permissions-display">
                    <div v-if="user?.permissions && user.permissions.length > 0" class="permissions-grid">
                      <div v-for="permission in user.permissions" :key="permission.name" class="permission-item">
                        <el-tag type="success" effect="plain" size="large" class="permission-tag">
                          <el-icon class="tag-icon"><Key /></el-icon>
                          <div class="tag-content">
                            <span class="tag-name">{{ permission.description || permission.name }}</span>
                            <span class="tag-code">{{ permission.name }}</span>
                          </div>
                        </el-tag>
                      </div>
                    </div>
                    <div v-else class="no-permissions">
                      <el-empty description="您当前没有特殊权限" :image-size="80">
                        <template #description>
                          <p>您拥有基本用户操作权限</p>
                          <p class="empty-tip">如需更多权限，请联系系统管理员</p>
                        </template>
                      </el-empty>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import {
  User, UserFilled, Edit, Camera, CircleCheck, Message, InfoFilled, Check, Refresh,
  Lock, Key, Warning, Document, View
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import api from '@/api/axios';

const authStore = useAuthStore();
const user = computed(() => authStore.user);

const activeTab = ref('info');
const loading = ref(false);
const profileFormRef = ref(null);
const passwordFormRef = ref(null);

const profileForm = ref({
  username: '',
  realName: '',
  email: ''
});

const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// Form validation rules
const profileFormRules = ref({
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
});

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入新密码'));
  } else if (value.length < 6) {
     callback(new Error('密码长度至少为6位'));
  } else {
    if (passwordForm.value.confirmPassword !== '') {
      if (!passwordFormRef.value) return;
      passwordFormRef.value.validateField('confirmPassword', () => null);
    }
    callback();
  }
};

const validatePass2 = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'));
  } else if (value !== passwordForm.value.newPassword) {
    callback(new Error("两次输入的密码不一致!"));
  } else {
    callback();
  }
};

const passwordFormRules = ref({
  currentPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
  newPassword: [{ validator: validatePass, trigger: 'blur' }],
  confirmPassword: [{ validator: validatePass2, trigger: 'blur' }],
});

// Utility functions
const formatRoleName = (role) => {
  const roleMap = {
    'ADMIN': '系统管理员',
    'MANAGER': '管理员',
    'USER': '普通用户',
    'ROLE_ADMIN': '系统管理员',
    'ROLE_MANAGER': '管理员',
    'ROLE_USER': '普通用户'
  };
  return roleMap[role] || role.replace(/^ROLE_/, '');
};

const getRoleDescription = (role) => {
  const descMap = {
    'ADMIN': '拥有系统所有权限',
    'MANAGER': '拥有管理权限',
    'USER': '基础用户权限',
    'ROLE_ADMIN': '拥有系统所有权限',
    'ROLE_MANAGER': '拥有管理权限',
    'ROLE_USER': '基础用户权限'
  };
  return descMap[role] || '自定义角色';
};

// formatPermission函数已移除，现在直接使用从数据库读取的权限描述

// Form methods
const handleEditProfile = () => {
  activeTab.value = 'info';
  // 聚焦到第一个可编辑的字段
  setTimeout(() => {
    const realNameInput = document.querySelector('input[placeholder*="真实姓名"]');
    if (realNameInput) {
      realNameInput.focus();
    }
  }, 100);
  ElMessage.info('请编辑您的个人信息');
};

const resetProfileForm = () => {
  if (user.value) {
    profileForm.value = {
      username: user.value.username || '',
      realName: user.value.realName || '',
      email: user.value.email || ''
    };
  }
  profileFormRef.value?.clearValidate();
};

const resetPasswordForm = () => {
  passwordForm.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  passwordFormRef.value?.resetFields();
};

const updateProfile = async () => {
  if (!profileFormRef.value) return;
  await profileFormRef.value.validate(async (valid) => {
     if (valid) {
        loading.value = true;
        try {
          // 调用真实的API来更新个人信息
          const updateData = {
            realName: profileForm.value.realName,
            email: profileForm.value.email
          };
          
          // 使用profile API端点进行更新
          await api.put('/api/profile', updateData);
          
          ElMessage.success('个人信息更新成功');
          await authStore.fetchUser(); // Refresh user info in store
        } catch (error) {
          console.error('更新个人信息失败:', error);
          ElMessage.error(error.response?.data?.message || '更新个人信息失败');
        } finally {
          loading.value = false;
        }
     } else {
       console.log('Profile form validation failed');
       return false;
     }
   });
};

const changePassword = async () => {
  if (!passwordFormRef.value) return;
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        // 调用真实的API来修改密码
        const passwordData = {
          currentPassword: passwordForm.value.currentPassword,
          newPassword: passwordForm.value.newPassword
        };
        
        // 使用profile API端点进行密码修改
        await api.put('/api/profile/password', passwordData);
        
        ElMessage.success('密码修改成功');
        resetPasswordForm();
      } catch (error) {
        console.error('密码修改失败:', error);
        ElMessage.error(error.response?.data?.message || '密码修改失败');
      } finally {
        loading.value = false;
      }
    } else {
      console.log('Password form validation failed');
      return false;
    }
  });
};

onMounted(() => {
   authStore.fetchUser().then(() => {
       resetProfileForm();
   });
});
</script>

<style scoped>
/* 全局样式 */
.profile-management {
  padding: 24px;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #409eff;
  font-size: 28px;
}

.page-description {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 用户概览 */
.user-overview {
  margin-bottom: 24px;
}

.overview-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 8px 0;
}

.avatar-section {
  flex-shrink: 0;
  text-align: center;
}

.user-avatar {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  margin-bottom: 8px;
}

.avatar-actions {
  margin-top: 8px;
}

.info-section {
  flex: 1;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-name {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.user-subtitle {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.role-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag-icon {
  font-size: 12px;
}

.status-section {
  flex-shrink: 0;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  min-width: 140px;
}

.status-label {
  font-size: 13px;
  color: #666;
}

.status-value {
  font-size: 13px;
  color: #1a1a1a;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  margin-bottom: 24px;
}

.content-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.profile-tabs {
  margin-top: 16px;
}

.tab-content {
  padding: 16px 0;
}

/* 表单样式 */
.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #409eff;
  border-radius: 2px;
}

.form-input {
  width: 100%;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 4px;
  color: #0288d1;
  font-size: 13px;
  line-height: 1.4;
}

/* 安全提示 */
.security-tips {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.tip-icon {
  color: #409eff;
  font-size: 20px;
  margin-top: 2px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.tip-desc {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

/* 权限展示 */
.permissions-overview {
  margin-top: 16px;
}

.roles-display, .permissions-display {
  margin-top: 16px;
}

.roles-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-item {
  width: 100%;
}

.role-tag-large, .permission-tag {
  width: 100%;
  padding: 16px;
  height: auto;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-tag-large {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.permission-tag {
  background: #f0f9f0;
  border-color: #67c23a;
  color: #67c23a;
}

.tag-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  flex: 1;
}

.tag-name {
  font-weight: 600;
  color: inherit;
  font-size: 14px;
}

.tag-desc, .tag-code {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.4;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.permission-item {
  width: 100%;
}

.no-roles, .no-permissions {
  padding: 40px 20px;
  text-align: center;
}

.empty-tip {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

/* 响应式 */
@media (max-width: 768px) {
  .profile-management {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .status-section {
    width: 100%;
  }
  
  .status-items {
    flex-direction: row;
    justify-content: space-around;
  }
  
  .permissions-grid {
    grid-template-columns: 1fr;
  }
}

/* Element Plus 组件覆盖 */
.el-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.el-tabs {
  margin-top: 0;
}

.el-tabs__header {
  margin-bottom: 16px;
}

.el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #f0f0f0;
}

.el-tabs__active-bar {
  background-color: #409eff;
}

.el-tabs__item.is-active {
  color: #409eff;
  font-weight: 600;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff, #67c23a);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #67c23a, #409eff);
}

.el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  color: #999;
}
</style>