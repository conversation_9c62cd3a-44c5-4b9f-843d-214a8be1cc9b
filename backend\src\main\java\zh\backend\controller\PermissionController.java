package zh.backend.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import zh.backend.annotation.RequirePermission;
import zh.backend.entity.Permission;
import zh.backend.service.PermissionService;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 权限管理控制器
 */
@RestController
@RequestMapping("/api/permissions")
@RequirePermission("PERMISSION_MANAGEMENT")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    /**
     * 获取权限列表
     */
    @GetMapping
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<Page<Permission>> getPermissions(
            @RequestParam(defaultValue = "") String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Permission> permissions = permissionService.findByKeyword(keyword, pageable);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 获取权限树
     */
    @GetMapping("/tree")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<List<Permission>> getPermissionTree() {
        List<Permission> tree = permissionService.buildPermissionTree();
        return ResponseEntity.ok(tree);
    }

    /**
     * 根据ID获取权限
     */
    @GetMapping("/{id}")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<Permission> getPermission(@PathVariable Long id) {
        Permission permission = permissionService.findById(id);
        return ResponseEntity.ok(permission);
    }

    /**
     * 创建权限
     */
    @PostMapping
    @RequirePermission("PERMISSION_CREATE")
    public ResponseEntity<Permission> createPermission(@Valid @RequestBody Permission permission) {
        Permission createdPermission = permissionService.createPermission(permission);
        return ResponseEntity.ok(createdPermission);
    }

    /**
     * 更新权限
     */
    @PutMapping("/{id}")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<Permission> updatePermission(
            @PathVariable Long id, 
            @Valid @RequestBody Permission permission) {
        Permission updatedPermission = permissionService.updatePermission(id, permission);
        return ResponseEntity.ok(updatedPermission);
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    @RequirePermission("PERMISSION_DELETE")
    public ResponseEntity<Void> deletePermission(@PathVariable Long id) {
        permissionService.deletePermission(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量删除权限
     */
    @DeleteMapping("/batch")
    @RequirePermission("PERMISSION_DELETE")
    public ResponseEntity<Void> deletePermissions(@RequestBody List<Long> ids) {
        permissionService.deletePermissions(ids);
        return ResponseEntity.ok().build();
    }

    /**
     * 根据资源类型获取权限
     */
    @GetMapping("/by-resource-type")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<List<Permission>> getPermissionsByResourceType(
            @RequestParam Permission.ResourceType resourceType) {
        List<Permission> permissions = permissionService.findByResourceType(resourceType);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 根据父权限ID获取子权限
     */
    @GetMapping("/children/{parentId}")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<List<Permission>> getChildPermissions(@PathVariable Long parentId) {
        List<Permission> children = permissionService.findByParentId(parentId);
        return ResponseEntity.ok(children);
    }

    /**
     * 检查权限编码是否存在
     */
    @GetMapping("/exists")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<Boolean> checkPermissionExists(@RequestParam String code) {
        boolean exists = permissionService.existsByCode(code);
        return ResponseEntity.ok(exists);
    }

    /**
     * 清除权限缓存
     */
    @PostMapping("/clear-cache")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<Void> clearPermissionCache() {
        permissionService.clearPermissionCache();
        return ResponseEntity.ok().build();
    }
}
