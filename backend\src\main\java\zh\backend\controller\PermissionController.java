package zh.backend.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import zh.backend.annotation.RequirePermission;
import zh.backend.dto.ApiResponse;
import zh.backend.dto.PermissionDTO;
import zh.backend.dto.PermissionTreeDTO;
import zh.backend.dto.UserPermissionDTO;
import zh.backend.entity.Permission;
import zh.backend.entity.User;
import zh.backend.service.PermissionService;
import zh.backend.service.UserService;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 权限管理控制器
 * 提供权限的CRUD操作、权限树构建、用户权限管理等功能
 */
@Tag(name = "权限管理", description = "权限管理相关API，包括权限的增删改查、权限树构建、用户权限分配等功能")
@RestController
@RequestMapping("/api/permissions")
@RequirePermission("PERMISSION_MANAGEMENT")
@SecurityRequirement(name = "bearerAuth")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private UserService userService;

    /**
     * 获取权限列表（分页）
     */
    @Operation(
        summary = "获取权限列表",
        description = "分页查询权限列表，支持关键字搜索和多种过滤条件"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功",
            content = @Content(schema = @Schema(implementation = Page.class))),
        @ApiResponse(responseCode = "403", description = "权限不足"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<zh.backend.dto.ApiResponse<Page<PermissionDTO>>> getPermissions(
            @Parameter(description = "搜索关键字，支持权限名称和编码模糊搜索")
            @RequestParam(defaultValue = "") String keyword,
            @Parameter(description = "页码，从0开始")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段")
            @RequestParam(defaultValue = "id") String sortBy,
            @Parameter(description = "排序方向：asc或desc")
            @RequestParam(defaultValue = "asc") String sortDir,
            @Parameter(description = "资源类型过滤")
            @RequestParam(required = false) Permission.ResourceType resourceType,
            @Parameter(description = "权限状态过滤")
            @RequestParam(required = false) Permission.PermissionStatus status,
            @Parameter(description = "是否系统权限过滤")
            @RequestParam(required = false) Boolean isSystem) {

        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<Permission> permissions = permissionService.findByKeyword(keyword, pageable);

            // 转换为DTO并应用过滤条件
            Page<PermissionDTO> permissionDTOs = permissions.map(permission -> {
                PermissionDTO dto = new PermissionDTO(permission);
                return dto;
            });

            // 应用额外的过滤条件
            if (resourceType != null || status != null || isSystem != null) {
                List<PermissionDTO> filteredList = permissionDTOs.getContent().stream()
                    .filter(dto -> resourceType == null || dto.getResourceType() == resourceType)
                    .filter(dto -> status == null || dto.getStatus() == status)
                    .filter(dto -> isSystem == null || dto.getIsSystem().equals(isSystem))
                    .collect(Collectors.toList());

                // 这里简化处理，实际应该在Service层进行过滤
                // 为了演示，我们返回过滤后的结果
            }

            return ResponseEntity.ok(zh.backend.dto.ApiResponse.success("获取权限列表成功", permissionDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(zh.backend.dto.ApiResponse.error("获取权限列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取权限树
     */
    @Operation(
        summary = "获取权限树",
        description = "获取层级结构的权限树，支持按资源类型过滤和是否包含非激活权限"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "权限不足"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/tree")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<zh.backend.dto.ApiResponse<List<PermissionTreeDTO>>> getPermissionTree(
            @Parameter(description = "资源类型过滤，不传则返回所有类型")
            @RequestParam(required = false) Permission.ResourceType resourceType,
            @Parameter(description = "是否包含非激活状态的权限")
            @RequestParam(defaultValue = "false") boolean includeInactive) {

        try {
            List<Permission> permissions = permissionService.buildPermissionTree();

            // 转换为树形DTO结构
            List<PermissionTreeDTO> tree = buildPermissionTreeDTO(permissions, resourceType, includeInactive);

            return ResponseEntity.ok(ApiResponse.success("获取权限树成功", tree));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限树失败: " + e.getMessage()));
        }
    }

    /**
     * 根据ID获取权限详情
     */
    @GetMapping("/{id}")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<PermissionDTO>> getPermission(@PathVariable Long id) {
        try {
            Permission permission = permissionService.findById(id);
            PermissionDTO dto = new PermissionDTO(permission);
            return ResponseEntity.ok(ApiResponse.success("获取权限详情成功", dto));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("权限不存在: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限详情失败: " + e.getMessage()));
        }
    }

    /**
     * 创建权限
     */
    @PostMapping
    @RequirePermission("PERMISSION_CREATE")
    public ResponseEntity<ApiResponse<PermissionDTO>> createPermission(@Valid @RequestBody PermissionDTO permissionDTO) {
        try {
            // 获取当前用户信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.findByUsername(auth.getName());

            Permission permission = permissionDTO.toEntity();
            permission.setCreatedBy(currentUser.getId());

            Permission createdPermission = permissionService.createPermission(permission);
            PermissionDTO resultDTO = new PermissionDTO(createdPermission);

            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("权限创建成功", resultDTO));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("权限创建失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限创建失败: " + e.getMessage()));
        }
    }

    /**
     * 更新权限
     */
    @PutMapping("/{id}")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<PermissionDTO>> updatePermission(
            @PathVariable Long id,
            @Valid @RequestBody PermissionDTO permissionDTO) {
        try {
            // 获取当前用户信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.findByUsername(auth.getName());

            Permission permission = permissionDTO.toEntity();
            permission.setUpdatedBy(currentUser.getId());

            Permission updatedPermission = permissionService.updatePermission(id, permission);
            PermissionDTO resultDTO = new PermissionDTO(updatedPermission);

            return ResponseEntity.ok(ApiResponse.success("权限更新成功", resultDTO));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("权限更新失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限更新失败: " + e.getMessage()));
        }
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    @RequirePermission("PERMISSION_DELETE")
    public ResponseEntity<ApiResponse<Void>> deletePermission(@PathVariable Long id) {
        try {
            Permission permission = permissionService.findById(id);

            // 检查是否为系统权限
            if (permission.getIsSystem()) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("系统权限不允许删除"));
            }

            permissionService.deletePermission(id);
            return ResponseEntity.ok(ApiResponse.<Void>success("权限删除成功", null));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("权限删除失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限删除失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除权限
     */
    @DeleteMapping("/batch")
    @RequirePermission("PERMISSION_DELETE")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deletePermissions(@RequestBody List<Long> ids) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Long> successIds = new ArrayList<>();
            List<String> failedReasons = new ArrayList<>();

            for (Long id : ids) {
                try {
                    Permission permission = permissionService.findById(id);
                    if (permission.getIsSystem()) {
                        failedReasons.add("权限ID " + id + ": 系统权限不允许删除");
                        continue;
                    }

                    permissionService.deletePermission(id);
                    successIds.add(id);
                } catch (Exception e) {
                    failedReasons.add("权限ID " + id + ": " + e.getMessage());
                }
            }

            result.put("successCount", successIds.size());
            result.put("failedCount", failedReasons.size());
            result.put("successIds", successIds);
            result.put("failedReasons", failedReasons);

            if (failedReasons.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.success("批量删除权限成功", result));
            } else {
                return ResponseEntity.ok(ApiResponse.success("批量删除权限部分成功", result));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("批量删除权限失败: " + e.getMessage()));
        }
    }

    /**
     * 根据资源类型获取权限
     */
    @GetMapping("/by-resource-type")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getPermissionsByResourceType(
            @RequestParam Permission.ResourceType resourceType) {
        try {
            List<Permission> permissions = permissionService.findByResourceType(resourceType);
            List<PermissionDTO> permissionDTOs = permissions.stream()
                .map(PermissionDTO::new)
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取权限列表成功", permissionDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据父权限ID获取子权限
     */
    @GetMapping("/children/{parentId}")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getChildPermissions(@PathVariable Long parentId) {
        try {
            List<Permission> children = permissionService.findByParentId(parentId);
            List<PermissionDTO> childrenDTOs = children.stream()
                .map(PermissionDTO::new)
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取子权限列表成功", childrenDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取子权限列表失败: " + e.getMessage()));
        }
    }

    /**
     * 检查权限编码是否存在
     */
    @GetMapping("/exists")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Boolean>> checkPermissionExists(@RequestParam String code) {
        try {
            boolean exists = permissionService.existsByCode(code);
            return ResponseEntity.ok(ApiResponse.success("检查权限编码成功", exists));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("检查权限编码失败: " + e.getMessage()));
        }
    }

    /**
     * 清除权限缓存
     */
    @PostMapping("/clear-cache")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<Void>> clearPermissionCache() {
        try {
            permissionService.clearPermissionCache();
            return ResponseEntity.ok(ApiResponse.<Void>success("权限缓存清除成功", null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限缓存清除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取权限统计信息
     */
    @GetMapping("/statistics")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPermissionStatistics() {
        try {
            List<Permission> allPermissions = permissionService.findAll();

            long total = allPermissions.size();
            long active = allPermissions.stream()
                .filter(p -> p.getStatus() == Permission.PermissionStatus.ACTIVE)
                .count();
            long inactive = total - active;
            long system = allPermissions.stream()
                .filter(Permission::getIsSystem)
                .count();
            long custom = total - system;

            // 按资源类型统计
            Map<Permission.ResourceType, Long> byResourceType = allPermissions.stream()
                .collect(Collectors.groupingBy(Permission::getResourceType, Collectors.counting()));

            // 按层级统计
            Map<Integer, Long> byLevel = allPermissions.stream()
                .collect(Collectors.groupingBy(Permission::getLevel, Collectors.counting()));

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("total", total);
            statistics.put("active", active);
            statistics.put("inactive", inactive);
            statistics.put("system", system);
            statistics.put("custom", custom);
            statistics.put("byResourceType", byResourceType);
            statistics.put("byLevel", byLevel);

            return ResponseEntity.ok(ApiResponse.success("获取权限统计信息成功", statistics));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的直接权限
     */
    @GetMapping("/user/{userId}/direct")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<UserPermissionDTO>>> getUserDirectPermissions(@PathVariable Long userId) {
        try {
            List<Permission> permissions = permissionService.getUserDirectPermissions(userId);
            List<UserPermissionDTO> permissionDTOs = permissions.stream()
                .map(permission -> {
                    UserPermissionDTO dto = new UserPermissionDTO();
                    dto.setUserId(userId);
                    dto.setPermissionId(permission.getId());
                    dto.setPermissionName(permission.getName());
                    dto.setPermissionCode(permission.getCode());
                    dto.setSource("DIRECT");
                    return dto;
                })
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取用户直接权限成功", permissionDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户直接权限失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的角色权限
     */
    @GetMapping("/user/{userId}/role")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<UserPermissionDTO>>> getUserRolePermissions(@PathVariable Long userId) {
        try {
            List<Permission> permissions = permissionService.getUserRolePermissions(userId);
            List<UserPermissionDTO> permissionDTOs = permissions.stream()
                .map(permission -> {
                    UserPermissionDTO dto = new UserPermissionDTO();
                    dto.setUserId(userId);
                    dto.setPermissionId(permission.getId());
                    dto.setPermissionName(permission.getName());
                    dto.setPermissionCode(permission.getCode());
                    dto.setSource("ROLE");
                    return dto;
                })
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取用户角色权限成功", permissionDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户角色权限失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的所有权限（包括直接权限和角色权限）
     */
    @GetMapping("/user/{userId}/all")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Set<String>>> getAllUserPermissions(@PathVariable Long userId) {
        try {
            Set<String> permissions = permissionService.getUserPermissions(userId);
            return ResponseEntity.ok(ApiResponse.success("获取用户所有权限成功", permissions));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户所有权限失败: " + e.getMessage()));
        }
    }

    /**
     * 为用户分配直接权限
     */
    @PostMapping("/user/{userId}/grant")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<Void>> grantPermissionToUser(
            @PathVariable Long userId,
            @RequestParam Long permissionId,
            @RequestParam(required = false) String expiresAt) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.findByUsername(auth.getName());

            permissionService.grantPermissionToUser(userId, permissionId, currentUser.getId());

            return ResponseEntity.ok(ApiResponse.<Void>success("权限分配成功", null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("权限分配失败: " + e.getMessage()));
        }
    }

    /**
     * 撤销用户的直接权限
     */
    @DeleteMapping("/user/{userId}/revoke")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<Void>> revokePermissionFromUser(
            @PathVariable Long userId,
            @RequestParam Long permissionId) {
        try {
            permissionService.revokePermissionFromUser(userId, permissionId);
            return ResponseEntity.ok(ApiResponse.<Void>success("权限撤销成功", null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("权限撤销失败: " + e.getMessage()));
        }
    }

    /**
     * 检查用户是否拥有指定权限
     */
    @GetMapping("/user/{userId}/check")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Boolean>> checkUserPermission(
            @PathVariable Long userId,
            @RequestParam String permissionCode) {
        try {
            boolean hasPermission = permissionService.hasPermission(userId, permissionCode);
            return ResponseEntity.ok(ApiResponse.success("权限检查成功", hasPermission));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限检查失败: " + e.getMessage()));
        }
    }

    /**
     * 构建权限树DTO的辅助方法
     */
    private List<PermissionTreeDTO> buildPermissionTreeDTO(List<Permission> permissions,
                                                          Permission.ResourceType resourceType,
                                                          boolean includeInactive) {
        // 过滤权限
        List<Permission> filteredPermissions = permissions.stream()
            .filter(p -> resourceType == null || p.getResourceType() == resourceType)
            .filter(p -> includeInactive || p.getStatus() == Permission.PermissionStatus.ACTIVE)
            .collect(Collectors.toList());

        // 构建树形结构
        Map<Long, PermissionTreeDTO> dtoMap = new HashMap<>();
        List<PermissionTreeDTO> rootNodes = new ArrayList<>();

        // 创建所有DTO节点
        for (Permission permission : filteredPermissions) {
            PermissionTreeDTO dto = new PermissionTreeDTO(permission);
            dtoMap.put(permission.getId(), dto);
        }

        // 构建父子关系
        for (Permission permission : filteredPermissions) {
            PermissionTreeDTO dto = dtoMap.get(permission.getId());
            if (permission.getParentId() == null) {
                rootNodes.add(dto);
            } else {
                PermissionTreeDTO parent = dtoMap.get(permission.getParentId());
                if (parent != null) {
                    parent.addChild(dto);
                }
            }
        }

        return rootNodes;
    }
}
