<template>
  <div class="permission-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权限名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入权限名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权限编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入权限编码"
              maxlength="100"
              show-word-limit
              :disabled="mode === 'edit'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="资源类型" prop="resourceType">
            <el-select v-model="formData.resourceType" placeholder="请选择资源类型" style="width: 100%">
              <el-option label="菜单" value="MENU" />
              <el-option label="按钮" value="BUTTON" />
              <el-option label="接口" value="API" />
              <el-option label="数据" value="DATA" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="HTTP方法" prop="httpMethod">
            <el-select v-model="formData.httpMethod" placeholder="请选择HTTP方法" style="width: 100%">
              <el-option label="ALL" value="ALL" />
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
              <el-option label="PUT" value="PUT" />
              <el-option label="DELETE" value="DELETE" />
              <el-option label="PATCH" value="PATCH" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="资源路径" prop="resourcePath">
        <el-input
          v-model="formData.resourcePath"
          placeholder="请输入资源路径，如：/api/users"
          maxlength="255"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="父权限" prop="parentId">
            <el-tree-select
              v-model="formData.parentId"
              :data="parentOptions"
              :props="treeProps"
              placeholder="请选择父权限"
              clearable
              check-strictly
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="层级" prop="level">
            <el-input-number
              v-model="formData.level"
              :min="1"
              :max="5"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number
              v-model="formData.sortOrder"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio value="ACTIVE">激活</el-radio>
              <el-radio value="INACTIVE">未激活</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="系统权限" prop="isSystem">
            <el-switch
              v-model="formData.isSystem"
              :disabled="mode !== 'create'"
            />
            <span class="form-tip">系统权限不可删除</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="权限描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入权限描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div class="form-actions" v-if="mode !== 'view'">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { permissionApi } from '@/api/permission'

// Props
const props = defineProps({
  permission: {
    type: Object,
    default: () => ({})
  },
  parentOptions: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'create', // create | edit | view
    validator: (value) => ['create', 'edit', 'view'].includes(value)
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  description: '',
  resourceType: 'API',
  resourcePath: '',
  httpMethod: 'ALL',
  parentId: null,
  level: 1,
  sortOrder: 0,
  status: 'ACTIVE',
  isSystem: false
})

// 树形选择器配置
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 100, message: '权限名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { min: 2, max: 100, message: '权限编码长度在 2 到 100 个字符', trigger: 'blur' },
    { pattern: /^[A-Z][A-Z0-9_]*$/, message: '权限编码只能包含大写字母、数字和下划线，且以大写字母开头', trigger: 'blur' },
    { validator: validateCode, trigger: 'blur' }
  ],
  resourceType: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请输入层级', trigger: 'blur' },
    { type: 'number', min: 1, max: 5, message: '层级必须在 1 到 5 之间', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, message: '排序不能小于 0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 自定义验证器
async function validateCode(rule, value, callback) {
  if (!value) {
    return callback()
  }
  
  try {
    const exists = await permissionApi.checkCodeExists(value)
    if (exists && props.mode === 'create') {
      callback(new Error('权限编码已存在'))
    } else if (exists && props.mode === 'edit' && value !== props.permission.code) {
      callback(new Error('权限编码已存在'))
    } else {
      callback()
    }
  } catch (error) {
    console.error('验证权限编码失败:', error)
    callback()
  }
}

// 监听props变化
watch(() => props.permission, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(formData, {
      name: newVal.name || '',
      code: newVal.code || '',
      description: newVal.description || '',
      resourceType: newVal.resourceType || 'API',
      resourcePath: newVal.resourcePath || '',
      httpMethod: newVal.httpMethod || 'ALL',
      parentId: newVal.parentId || null,
      level: newVal.level || 1,
      sortOrder: newVal.sortOrder || 0,
      status: newVal.status || 'ACTIVE',
      isSystem: newVal.isSystem || false
    })
  }
}, { immediate: true, deep: true })

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

// 暴露方法
defineExpose({
  resetForm
})
</script>

<style scoped>
.permission-form {
  padding: 20px 0;
}

.form-actions {
  margin-top: 30px;
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.form-actions .el-button {
  margin-left: 12px;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  resize: vertical;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-form {
    padding: 10px 0;
  }
  
  .el-form {
    padding: 0 10px;
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 10px;
  }
  
  .form-actions .el-button {
    margin: 0 6px;
  }
}
</style>
