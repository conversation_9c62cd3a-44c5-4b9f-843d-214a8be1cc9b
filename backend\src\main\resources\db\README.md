# 数据库迁移文件说明

## 📁 文件结构

```
db/
├── migration/
│   ├── V1__init_permission_system.sql     # 创建表结构
│   ├── V2__init_permission_data.sql       # 初始化基础数据（已废弃）
│   └── V3__complete_permission_data.sql   # 完整权限数据
└── README.md                              # 本文件
```

## 🚀 迁移文件说明

### V1__init_permission_system.sql
- **作用**: 创建完整的数据库表结构
- **包含表**:
  - `users` - 用户表
  - `roles` - 角色表
  - `permissions` - 权限表
  - `user_roles` - 用户角色关联表
  - `role_permissions` - 角色权限关联表
  - `user_permissions` - 用户直接权限表
  - `permission_policies` - 权限策略表
  - `permission_logs` - 权限操作日志表

### V2__init_permission_data.sql (已废弃)
- **状态**: 已废弃，存在数据插入格式问题
- **问题**: INSERT语句格式不正确，导致Flyway验证失败
- **替代**: 使用V3版本

### V3__complete_permission_data.sql (推荐使用)
- **作用**: 插入完整的权限体系数据
- **包含数据**:
  - 93个权限记录（7个模块，3个层级）
  - 3个系统角色
  - 3个示例用户
  - 完整的角色权限分配
  - 权限策略配置

## 📊 数据统计

### 权限数据
- **总权限数**: 93个
- **模块级权限**: 7个 (Level 1)
- **功能级权限**: 86个 (Level 2)
- **按钮级权限**: 0个 (Level 3, 预留)

### 角色数据
- **超级管理员**: 拥有所有93个权限
- **系统管理员**: 拥有85个管理权限
- **普通用户**: 拥有8个基础权限

### 用户数据
- **admin**: 超级管理员，密码 admin123
- **sysadmin**: 系统管理员，密码 sysadmin123
- **user**: 普通用户，密码 user123

## 🔧 使用方式

### 方式一：Flyway自动迁移（推荐）
1. 启用Flyway配置
```yaml
spring:
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
```

2. 启动应用，Flyway自动执行迁移

### 方式二：手动执行SQL
1. 禁用Flyway
```yaml
spring:
  flyway:
    enabled: false
```

2. 手动执行SQL文件
```bash
mysql -u root -p your_database < V1__init_permission_system.sql
mysql -u root -p your_database < V3__complete_permission_data.sql
```

### 方式三：使用DataInitializer（当前使用）
1. 禁用Flyway
2. 使用JPA自动创建表结构
3. DataInitializer自动初始化数据

## ⚠️ 注意事项

### 开发环境
- 使用 `ddl-auto: create-drop` 自动重建表
- DataInitializer每次启动都会重新初始化数据
- 适合开发和测试

### 生产环境
- 使用 `ddl-auto: none` 禁用自动DDL
- 启用Flyway进行版本化迁移
- 确保数据安全和一致性

### 数据安全
- 生产环境请修改默认密码
- 删除或禁用示例用户账户
- 定期备份权限配置

## 🔄 版本升级

### 添加新权限
1. 创建新的迁移文件 `V4__add_new_permissions.sql`
2. 插入新权限数据
3. 更新角色权限分配

### 修改权限结构
1. 创建新的迁移文件
2. 使用ALTER TABLE语句修改表结构
3. 迁移现有数据

### 示例：添加新权限
```sql
-- V4__add_new_permissions.sql
INSERT INTO permissions (name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
('新功能', 'NEW_FEATURE', '新功能描述', 'MENU', '/admin/new-feature', 'ALL', NULL, 1, 8, TRUE);

-- 为超级管理员分配新权限
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 1, p.id, 1 FROM permissions p WHERE p.code = 'NEW_FEATURE';
```

## 📋 检查清单

### 部署前检查
- [ ] 确认数据库连接配置正确
- [ ] 检查Flyway配置是否启用
- [ ] 验证迁移文件语法正确
- [ ] 备份现有数据库（生产环境）

### 部署后验证
- [ ] 检查所有表是否创建成功
- [ ] 验证权限数据是否正确插入
- [ ] 测试默认用户登录
- [ ] 验证权限控制是否生效

## 🆘 故障排除

### 常见问题

1. **Flyway验证失败**
   - 检查SQL语法是否正确
   - 确认文件编码为UTF-8
   - 验证数据库连接

2. **权限数据重复**
   - 清空相关表数据
   - 重新执行迁移脚本

3. **用户无法登录**
   - 检查密码加密是否正确
   - 验证用户状态是否为ACTIVE
   - 确认角色分配是否正确

### 重置数据库
```sql
-- 清空所有数据
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS permission_logs;
DROP TABLE IF EXISTS permission_policies;
DROP TABLE IF EXISTS user_permissions;
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS users;
SET FOREIGN_KEY_CHECKS = 1;

-- 重新执行迁移
-- 执行 V1__init_permission_system.sql
-- 执行 V3__complete_permission_data.sql
```
