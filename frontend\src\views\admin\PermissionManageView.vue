<template>
  <div class="permission-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>权限管理</h2>
        <p>管理系统权限，包括菜单、按钮、API和数据权限</p>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleCreate"
          v-can="'PERMISSION_CREATE'"
        >
          新增权限
        </el-button>
        <el-button
          :icon="Refresh"
          @click="refreshCache"
          v-can="'PERMISSION_UPDATE'"
        >
          刷新缓存
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索权限名称或编码"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.resourceType"
            placeholder="资源类型"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部" value="" />
            <el-option label="菜单" value="MENU" />
            <el-option label="按钮" value="BUTTON" />
            <el-option label="接口" value="API" />
            <el-option label="数据" value="DATA" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部" value="" />
            <el-option label="激活" value="ACTIVE" />
            <el-option label="未激活" value="INACTIVE" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="viewMode"
            placeholder="视图模式"
            @change="handleViewModeChange"
          >
            <el-option label="树形视图" value="tree" />
            <el-option label="列表视图" value="list" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="RefreshRight" @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 权限树形视图 -->
    <div v-if="viewMode === 'tree'" class="tree-section">
      <el-tree
        ref="permissionTreeRef"
        :data="permissionTree"
        :props="treeProps"
        node-key="id"
        :expand-on-click-node="false"
        :default-expand-all="false"
        :filter-node-method="filterNode"
        class="permission-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <div class="node-content">
              <el-tag
                :type="getResourceTypeColor(data.resourceType)"
                size="small"
                class="resource-tag"
              >
                {{ getResourceTypeLabel(data.resourceType) }}
              </el-tag>
              <span class="node-label">{{ data.name }}</span>
              <el-tag
                size="small"
                class="code-tag"
              >
                {{ data.code }}
              </el-tag>
              <el-tag
                :type="data.status === 'ACTIVE' ? 'success' : 'danger'"
                size="small"
              >
                {{ data.status === 'ACTIVE' ? '激活' : '未激活' }}
              </el-tag>
              <el-tag
                v-if="data.isSystem"
                type="warning"
                size="small"
              >
                系统
              </el-tag>
            </div>
            <div class="node-actions">
              <el-button
                type="primary"
                size="small"
                text
                @click="handleView(data)"
                v-can="'PERMISSION_LIST'"
              >
                查看
              </el-button>
              <el-button
                type="primary"
                size="small"
                text
                @click="handleEdit(data)"
                v-can="'PERMISSION_UPDATE'"
                :disabled="data.isSystem"
              >
                编辑
              </el-button>
              <el-button
                type="success"
                size="small"
                text
                @click="handleCreateChild(data)"
                v-can="'PERMISSION_CREATE'"
              >
                添加子权限
              </el-button>
              <el-button
                type="danger"
                size="small"
                text
                @click="handleDelete(data)"
                v-can="'PERMISSION_DELETE'"
                :disabled="data.isSystem || (data.children && data.children.length > 0)"
              >
                删除
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 权限列表视图 -->
    <div v-else class="table-section">
      <el-table
        :data="permissionList"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        class="permission-table"
      >
        <el-table-column prop="name" label="权限名称" min-width="200">
          <template #default="{ row }">
            <div class="permission-name">
              <el-tag
                :type="getResourceTypeColor(row.resourceType)"
                size="small"
                class="resource-tag"
              >
                {{ getResourceTypeLabel(row.resourceType) }}
              </el-tag>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="权限编码" width="200">
          <template #default="{ row }">
            <el-tag size="small" class="code-tag">{{ row.code }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resourcePath" label="资源路径" width="200" />
        <el-table-column prop="httpMethod" label="HTTP方法" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.httpMethod"
              :type="getHttpMethodColor(row.httpMethod)"
              size="small"
            >
              {{ row.httpMethod }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="层级" width="80" />
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'ACTIVE' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.status === 'ACTIVE' ? '激活' : '未激活' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="系统权限" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isSystem" type="warning" size="small">系统</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleView(row)"
              v-can="'PERMISSION_LIST'"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              size="small"
              text
              @click="handleEdit(row)"
              v-can="'PERMISSION_UPDATE'"
              :disabled="row.isSystem"
            >
              编辑
            </el-button>
            <el-button
              type="success"
              size="small"
              text
              @click="handleCreateChild(row)"
              v-can="'PERMISSION_CREATE'"
            >
              添加子权限
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="handleDelete(row)"
              v-can="'PERMISSION_DELETE'"
              :disabled="row.isSystem || (row.children && row.children.length > 0)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 权限详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <permission-form
        ref="permissionFormRef"
        :permission="currentPermission"
        :parent-options="parentOptions"
        :mode="dialogMode"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, RefreshRight } from '@element-plus/icons-vue'
import { permissionApi } from '@/api/permission'
import PermissionForm from '@/components/admin/PermissionForm.vue'
import { usePermission } from '@/composables/usePermission'

// 权限检查
const { hasPermission } = usePermission()

// 响应式数据
const loading = ref(false)
const viewMode = ref('tree')
const permissionTree = ref([])
const permissionList = ref([])
const permissionTreeRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  resourceType: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const dialogMode = ref('view')
const currentPermission = ref({})
const parentOptions = ref([])

// 树形配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    view: '查看权限',
    create: '新增权限',
    edit: '编辑权限'
  }
  return titles[dialogMode.value]
})

// 生命周期
onMounted(() => {
  fetchData()
})

// 获取数据
const fetchData = async () => {
  if (viewMode.value === 'tree') {
    await fetchPermissionTree()
  } else {
    await fetchPermissionList()
  }
}

// 获取权限树
const fetchPermissionTree = async () => {
  try {
    loading.value = true
    const response = await permissionApi.getPermissionTree()
    permissionTree.value = response.data || []
  } catch (error) {
    console.error('获取权限树失败:', error)
    ElMessage.error('获取权限树失败')
  } finally {
    loading.value = false
  }
}

// 获取权限列表
const fetchPermissionList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      keyword: searchForm.keyword,
      resourceType: searchForm.resourceType,
      status: searchForm.status
    }

    const response = await permissionApi.getPermissions(params)
    permissionList.value = response.data.content || []
    pagination.total = response.data.totalElements || 0
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败')
  } finally {
    loading.value = false
  }
}

// 创建权限
const handleCreate = () => {
  currentPermission.value = {
    name: '',
    code: '',
    description: '',
    resourceType: 'API',
    resourcePath: '',
    httpMethod: 'ALL',
    parentId: null,
    level: 1,
    sortOrder: 0,
    status: 'ACTIVE',
    isSystem: false
  }
  dialogMode.value = 'create'
  dialogVisible.value = true
  loadParentOptions()
}

// 编辑权限
const handleEdit = (permission) => {
  currentPermission.value = { ...permission }
  dialogMode.value = 'edit'
  dialogVisible.value = true
  loadParentOptions()
}

// 查看权限
const handleView = (permission) => {
  currentPermission.value = { ...permission }
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 删除权限
const handleDelete = async (permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${permission.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await permissionApi.deletePermission(permission.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
      ElMessage.error('删除权限失败')
    }
  }
}

// 创建子权限
const handleCreateChild = (parent) => {
  currentPermission.value = {
    name: '',
    code: '',
    description: '',
    resourceType: 'API',
    resourcePath: '',
    httpMethod: 'ALL',
    parentId: parent.id,
    level: (parent.level || 1) + 1,
    sortOrder: 0,
    status: 'ACTIVE',
    isSystem: false
  }
  dialogMode.value = 'create'
  dialogVisible.value = true
  loadParentOptions()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()

  // 树形视图过滤
  if (viewMode.value === 'tree' && permissionTreeRef.value) {
    permissionTreeRef.value.filter(searchForm.keyword)
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.resourceType = ''
  searchForm.status = ''
  pagination.page = 1
  fetchData()

  // 清除树形过滤
  if (viewMode.value === 'tree' && permissionTreeRef.value) {
    permissionTreeRef.value.filter('')
  }
}

// 切换视图模式
const handleViewModeChange = () => {
  fetchData()
}

// 刷新缓存
const refreshCache = async () => {
  try {
    await permissionApi.clearCache()
    ElMessage.success('缓存刷新成功')
  } catch (error) {
    console.error('刷新缓存失败:', error)
    ElMessage.error('刷新缓存失败')
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 表单提交
const handleSubmit = async (formData) => {
  try {
    if (dialogMode.value === 'create') {
      await permissionApi.createPermission(formData)
      ElMessage.success('创建成功')
    } else if (dialogMode.value === 'edit') {
      await permissionApi.updatePermission(currentPermission.value.id, formData)
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
}

// 树形过滤
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value) || data.code.includes(value)
}

// 加载父权限选项
const loadParentOptions = async () => {
  try {
    const response = await permissionApi.getPermissionTree()
    parentOptions.value = response.data || []
  } catch (error) {
    console.error('加载父权限选项失败:', error)
  }
}

// 工具方法
const getResourceTypeColor = (type) => {
  const colors = {
    MENU: 'primary',
    BUTTON: 'success',
    API: 'warning',
    DATA: 'info'
  }
  return colors[type] || 'primary'
}

const getResourceTypeLabel = (type) => {
  const labels = {
    MENU: '菜单',
    BUTTON: '按钮',
    API: '接口',
    DATA: '数据'
  }
  return labels[type] || type
}

const getHttpMethodColor = (method) => {
  const colors = {
    GET: 'success',
    POST: 'primary',
    PUT: 'warning',
    DELETE: 'danger',
    PATCH: 'info',
    ALL: ''
  }
  return colors[method] || 'primary'
}
</script>

<style scoped>
.permission-manage {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.tree-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.permission-tree {
  margin-top: 20px;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
  border-bottom: 1px solid #f0f2f5;
}

.tree-node:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.node-label {
  font-weight: 500;
  color: #303133;
  margin-right: 8px;
}

.resource-tag {
  margin-right: 8px;
  font-size: 12px;
}

.code-tag {
  background: #f0f2f5;
  color: #606266;
  border: none;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.node-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.permission-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-table {
  margin-top: 20px;
}

.permission-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-manage {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-start;
  }

  .search-section .el-row {
    flex-direction: column;
  }

  .search-section .el-col {
    width: 100% !important;
    margin-bottom: 12px;
  }

  .tree-node {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .node-actions {
    opacity: 1;
    width: 100%;
    justify-content: flex-start;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .search-section {
    background: #1d1e1f;
    border-color: #414243;
  }

  .tree-section,
  .table-section {
    background: #1d1e1f;
    border-color: #414243;
  }

  .tree-node:hover {
    background-color: #2a2b2c;
  }

  .code-tag {
    background: #2a2b2c;
    color: #a8abb2;
  }
}

/* 自定义滚动条 */
.permission-tree::-webkit-scrollbar {
  width: 6px;
}

.permission-tree::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.permission-tree::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.permission-tree::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.tree-node {
  transition: all 0.3s ease;
}

.resource-tag,
.code-tag {
  transition: all 0.2s ease;
}

.resource-tag:hover,
.code-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
