<template>
  <div class="permission-manage">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <span class="title">权限管理</span>
          <el-button type="primary" @click="handleAdd">新建权限</el-button>
        </div>
      </template>

      <!-- Search Bar -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索权限名称或编码"
          class="search-input"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>

      <!-- Permission Tree Table -->
      <el-table
        v-loading="loading"
        :data="permissionList"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="code" label="权限编码" min-width="150" />
        <el-table-column prop="name" label="权限名称" min-width="150" />
        <el-table-column prop="resourceType" label="资源类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getResourceTypeTag(row.resourceType)">
              {{ formatResourceType(row.resourceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resourcePath" label="资源路径" min-width="200" />
        <el-table-column prop="httpMethod" label="HTTP方法" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.httpMethod" :type="getMethodTag(row.httpMethod)">
              {{ row.httpMethod }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'info'">
              {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                type="primary"
                link
                :disabled="row.isSystem"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="primary"
                link
                @click="handleAddChild(row)"
              >
                添加子权限
              </el-button>
              <el-button
                type="danger"
                link
                :disabled="row.isSystem"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Permission Form Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="formTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent
      >
        <el-form-item label="权限编码" prop="code">
          <el-input
            v-model="form.code"
            placeholder="请输入权限编码"
            :disabled="form.isSystem"
          />
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入权限名称"
          />
        </el-form-item>
        <el-form-item label="资源类型" prop="resourceType">
          <el-select v-model="form.resourceType" placeholder="请选择资源类型">
            <el-option label="菜单" value="MENU" />
            <el-option label="按钮" value="BUTTON" />
            <el-option label="API" value="API" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="资源路径" prop="resourcePath">
          <el-input
            v-model="form.resourcePath"
            placeholder="请输入资源路径"
          />
        </el-form-item>
        <el-form-item label="HTTP方法" prop="httpMethod">
          <el-select
            v-model="form.httpMethod"
            placeholder="请选择HTTP方法"
            clearable
          >
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
            <el-option label="PATCH" value="PATCH" />
          </el-select>
        </el-form-item>
        <el-form-item label="父级权限" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="permissionOptions"
            placeholder="请选择父级权限"
            clearable
            check-strictly
            :render-after-expand="false"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            :max="9999"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="ACTIVE">启用</el-radio>
            <el-radio label="INACTIVE">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { permissionService } from '@/api/permissionService'

// Data
const loading = ref(false)
const dialogVisible = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchKeyword = ref('')
const permissionList = ref([])
const permissionOptions = ref([])

// Form
const formRef = ref(null)
const formMode = ref('create')
const form = ref({
  id: null,
  code: '',
  name: '',
  description: '',
  resourceType: '',
  resourcePath: '',
  httpMethod: '',
  parentId: null,
  level: 1,
  sortOrder: 0,
  status: 'ACTIVE',
  isSystem: false
})

// Computed
const formTitle = computed(() => {
  return formMode.value === 'create' ? '新建权限' : '编辑权限'
})

// Validation rules
const rules = {
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  resourceType: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ]
}

// Methods
const loadPermissions = async () => {
  try {
    loading.value = true
    const response = await permissionService.getPermissions({
      keyword: searchKeyword.value,
      page: currentPage.value - 1,
      size: pageSize.value
    })
    permissionList.value = response.content
    total.value = response.totalElements
  } catch (error) {
    ElMessage.error('加载权限列表失败')
    console.error('Failed to load permissions:', error)
  } finally {
    loading.value = false
  }
}

const loadPermissionTree = async () => {
  try {
    const tree = await permissionService.getPermissionTree()
    permissionOptions.value = tree
  } catch (error) {
    ElMessage.error('加载权限树失败')
    console.error('Failed to load permission tree:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadPermissions()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  loadPermissions()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadPermissions()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.value = {
    id: null,
    code: '',
    name: '',
    description: '',
    resourceType: '',
    resourcePath: '',
    httpMethod: '',
    parentId: null,
    level: 1,
    sortOrder: 0,
    status: 'ACTIVE',
    isSystem: false
  }
}

const handleAdd = () => {
  formMode.value = 'create'
  resetForm()
  dialogVisible.value = true
}

const handleAddChild = (row) => {
  formMode.value = 'create'
  resetForm()
  form.value.parentId = row.id
  form.value.level = row.level + 1
  dialogVisible.value = true
}

const handleEdit = (row) => {
  formMode.value = 'edit'
  resetForm()
  Object.assign(form.value, row)
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  if (row.isSystem) {
    ElMessage.warning('系统权限不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除该权限吗？删除后不可恢复。',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await permissionService.deletePermission(row.id)
    ElMessage.success('删除成功')
    loadPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('Failed to delete permission:', error)
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (formMode.value === 'create') {
      await permissionService.createPermission(form.value)
      ElMessage.success('创建成功')
    } else {
      await permissionService.updatePermission(form.value.id, form.value)
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    loadPermissions()
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '操作失败')
    console.error('Failed to submit form:', error)
  }
}

const getResourceTypeTag = (type) => {
  const types = {
    MENU: 'success',
    BUTTON: 'warning',
    API: 'info',
    OTHER: ''
  }
  return types[type] || ''
}

const formatResourceType = (type) => {
  const types = {
    MENU: '菜单',
    BUTTON: '按钮',
    API: '接口',
    OTHER: '其他'
  }
  return types[type] || type
}

const getMethodTag = (method) => {
  const methods = {
    GET: '',
    POST: 'success',
    PUT: 'warning',
    DELETE: 'danger',
    PATCH: 'info'
  }
  return methods[method] || ''
}

// Lifecycle
onMounted(() => {
  loadPermissions()
  loadPermissionTree()
})
</script>

<style scoped>
.permission-manage {
  padding: 20px;
}

.page-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-bar {
  margin-bottom: 20px;
}

.search-input {
  width: 300px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-tree-select) {
  width: 100%;
}
</style>
