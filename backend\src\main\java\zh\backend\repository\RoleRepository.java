package zh.backend.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zh.backend.entity.Role;

import java.util.List;
import java.util.Optional;

/**
 * 角色数据访问接口
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {

    /**
     * 根据角色编码查找角色
     */
    Optional<Role> findByCode(String code);

    /**
     * 根据角色名称查找角色
     */
    Optional<Role> findByName(String name);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 根据状态查找角色
     */
    List<Role> findByStatus(Role.RoleStatus status);

    /**
     * 查找系统内置角色
     */
    List<Role> findByIsSystemTrue();

    /**
     * 查找非系统角色
     */
    List<Role> findByIsSystemFalse();

    /**
     * 根据用户ID查找角色
     */
    @Query("SELECT r FROM Role r JOIN r.users u WHERE u.id = :userId")
    List<Role> findByUserId(@Param("userId") Long userId);

    /**
     * 根据权限查找角色
     */
    @Query("SELECT DISTINCT r FROM Role r JOIN r.permissions p WHERE p.code = :permissionCode")
    List<Role> findByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 分页查询角色（支持关键字搜索）
     */
    @Query("SELECT r FROM Role r WHERE " +
           "(:keyword IS NULL OR :keyword = '' OR " +
           "r.name LIKE %:keyword% OR " +
           "r.code LIKE %:keyword% OR " +
           "r.description LIKE %:keyword%)")
    Page<Role> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 按排序顺序查找激活的角色
     */
    @Query("SELECT r FROM Role r WHERE r.status = 'ACTIVE' ORDER BY r.sortOrder ASC, r.name ASC")
    List<Role> findActiveRolesOrderBySortOrder();

    /**
     * 统计角色数量
     */
    @Query("SELECT COUNT(r) FROM Role r WHERE r.status = :status")
    long countByStatus(@Param("status") Role.RoleStatus status);

    /**
     * 查找拥有特定权限的角色数量
     */
    @Query("SELECT COUNT(DISTINCT r) FROM Role r JOIN r.permissions p WHERE p.code = :permissionCode")
    long countByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 查找角色及其权限数量
     */
    @Query("SELECT r, COUNT(p) FROM Role r LEFT JOIN r.permissions p GROUP BY r")
    List<Object[]> findRolesWithPermissionCount();
}
