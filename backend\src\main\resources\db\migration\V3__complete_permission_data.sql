-- 动态权限管理系统 - 完整权限数据
-- 版本: 3.0
-- 更新时间: 2024-12-19
-- 说明: 修复V2版本的数据插入问题，提供完整的权限体系

-- 清空现有权限数据
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM role_permissions;
DELETE FROM user_permissions;
DELETE FROM permissions;
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 完整权限数据插入
-- ========================================

-- 1. 用户管理模块权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(1, '用户管理', 'USER_MANAGEMENT', '用户管理模块', 'MENU', '/admin/users', 'ALL', NULL, 1, 1, TRUE);

INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(2, '查看用户列表', 'USER_LIST', '查看用户列表', 'API', '/api/users', 'GET', 1, 2, 1, TRUE),
(3, '创建用户', 'USER_CREATE', '创建新用户', 'API', '/api/users', 'POST', 1, 2, 2, TRUE),
(4, '编辑用户', 'USER_UPDATE', '编辑用户信息', 'API', '/api/users/*', 'PUT', 1, 2, 3, TRUE),
(5, '删除用户', 'USER_DELETE', '删除用户', 'API', '/api/users/*', 'DELETE', 1, 2, 4, TRUE),
(6, '重置用户密码', 'USER_RESET_PASSWORD', '重置用户密码', 'API', '/api/users/*/reset-password', 'POST', 1, 2, 5, TRUE),
(7, '用户状态管理', 'USER_STATUS_MANAGE', '管理用户状态', 'API', '/api/users/*/status', 'PATCH', 1, 2, 6, TRUE),
(8, '用户角色分配', 'USER_ROLE_ASSIGN', '为用户分配角色', 'API', '/api/users/*/roles', 'POST', 1, 2, 7, TRUE);

-- 2. 角色管理模块权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(10, '角色管理', 'ROLE_MANAGEMENT', '角色管理模块', 'MENU', '/admin/roles', 'ALL', NULL, 1, 2, TRUE);

INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(11, '查看角色列表', 'ROLE_LIST', '查看角色列表', 'API', '/api/roles', 'GET', 10, 2, 1, TRUE),
(12, '创建角色', 'ROLE_CREATE', '创建新角色', 'API', '/api/roles', 'POST', 10, 2, 2, TRUE),
(13, '编辑角色', 'ROLE_UPDATE', '编辑角色信息', 'API', '/api/roles/*', 'PUT', 10, 2, 3, TRUE),
(14, '删除角色', 'ROLE_DELETE', '删除角色', 'API', '/api/roles/*', 'DELETE', 10, 2, 4, TRUE),
(15, '分配角色权限', 'ROLE_ASSIGN_PERMISSIONS', '为角色分配权限', 'API', '/api/roles/*/permissions', 'POST', 10, 2, 5, TRUE),
(16, '角色用户管理', 'ROLE_USER_MANAGE', '管理角色下的用户', 'API', '/api/roles/*/users', 'POST', 10, 2, 6, TRUE);

-- 3. 权限管理模块权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(20, '权限管理', 'PERMISSION_MANAGEMENT', '权限管理模块', 'MENU', '/admin/permissions', 'ALL', NULL, 1, 3, TRUE);

INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(21, '查看权限列表', 'PERMISSION_LIST', '查看权限列表', 'API', '/api/permissions', 'GET', 20, 2, 1, TRUE),
(22, '创建权限', 'PERMISSION_CREATE', '创建新权限', 'API', '/api/permissions', 'POST', 20, 2, 2, TRUE),
(23, '编辑权限', 'PERMISSION_UPDATE', '编辑权限信息', 'API', '/api/permissions/*', 'PUT', 20, 2, 3, TRUE),
(24, '删除权限', 'PERMISSION_DELETE', '删除权限', 'API', '/api/permissions/*', 'DELETE', 20, 2, 4, TRUE),
(25, '权限树查看', 'PERMISSION_TREE', '查看权限树形结构', 'API', '/api/permissions/tree', 'GET', 20, 2, 5, TRUE);

-- 4. 系统监控权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(30, '系统监控', 'SYSTEM_MONITOR', '系统监控模块', 'MENU', '/admin/monitor', 'ALL', NULL, 1, 4, TRUE);

INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(31, '查看系统日志', 'LOG_VIEW', '查看系统操作日志', 'API', '/api/logs', 'GET', 30, 2, 1, TRUE),
(32, '在线用户管理', 'ONLINE_USERS', '查看和管理在线用户', 'API', '/api/online-users', 'GET', 30, 2, 2, TRUE),
(33, '系统健康检查', 'HEALTH_CHECK', '查看系统健康状态', 'API', '/api/health', 'GET', 30, 2, 3, TRUE);

-- 5. 个人中心权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(40, '个人中心', 'PROFILE', '个人中心模块', 'MENU', '/profile', 'ALL', NULL, 1, 5, TRUE);

INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(41, '查看个人信息', 'PROFILE_VIEW', '查看个人信息', 'API', '/api/profile', 'GET', 40, 2, 1, TRUE),
(42, '修改个人信息', 'PROFILE_UPDATE', '修改个人信息', 'API', '/api/profile', 'PUT', 40, 2, 2, TRUE),
(43, '修改密码', 'PASSWORD_CHANGE', '修改个人密码', 'API', '/api/profile/password', 'PUT', 40, 2, 3, TRUE);

-- 6. 仪表盘权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(50, '仪表盘', 'DASHBOARD', '仪表盘模块', 'MENU', '/dashboard', 'ALL', NULL, 1, 6, TRUE);

INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(51, '查看统计数据', 'DASHBOARD_STATS', '查看仪表盘统计数据', 'API', '/api/dashboard/stats', 'GET', 50, 2, 1, TRUE),
(52, '查看系统状态', 'DASHBOARD_SYSTEM_STATUS', '查看系统运行状态', 'API', '/api/dashboard/system-status', 'GET', 50, 2, 2, TRUE);

-- 7. 认证相关权限
INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(60, '认证管理', 'AUTH_MANAGEMENT', '认证管理模块', 'API', '/api/auth', 'ALL', NULL, 1, 7, TRUE);

INSERT INTO permissions (id, name, code, description, resource_type, resource_path, http_method, parent_id, level, sort_order, is_system) VALUES
(61, '用户登录', 'AUTH_LOGIN', '用户登录', 'API', '/api/auth/login', 'POST', 60, 2, 1, TRUE),
(62, '用户注册', 'AUTH_REGISTER', '用户注册', 'API', '/api/auth/register', 'POST', 60, 2, 2, TRUE),
(63, '用户登出', 'AUTH_LOGOUT', '用户登出', 'API', '/api/auth/logout', 'POST', 60, 2, 3, TRUE),
(64, '获取用户信息', 'AUTH_ME', '获取当前用户信息', 'API', '/api/auth/me', 'GET', 60, 2, 4, TRUE);

-- ========================================
-- 重新分配角色权限
-- ========================================

-- 为超级管理员分配所有权限
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 1, p.id, 1 FROM permissions p;

-- 为系统管理员分配管理权限
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 2, p.id, 1
FROM permissions p
WHERE p.code IN (
    'USER_MANAGEMENT', 'USER_LIST', 'USER_CREATE', 'USER_UPDATE', 'USER_RESET_PASSWORD', 'USER_STATUS_MANAGE', 'USER_ROLE_ASSIGN',
    'ROLE_MANAGEMENT', 'ROLE_LIST', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_ASSIGN_PERMISSIONS', 'ROLE_USER_MANAGE',
    'PERMISSION_MANAGEMENT', 'PERMISSION_LIST', 'PERMISSION_TREE',
    'SYSTEM_MONITOR', 'LOG_VIEW', 'ONLINE_USERS', 'HEALTH_CHECK',
    'DASHBOARD', 'DASHBOARD_STATS', 'DASHBOARD_SYSTEM_STATUS',
    'PROFILE', 'PROFILE_VIEW', 'PROFILE_UPDATE', 'PASSWORD_CHANGE',
    'AUTH_MANAGEMENT', 'AUTH_LOGIN', 'AUTH_LOGOUT', 'AUTH_ME'
);

-- 为普通用户分配基础权限
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 3, p.id, 1
FROM permissions p
WHERE p.code IN (
    'DASHBOARD', 'DASHBOARD_STATS',
    'PROFILE', 'PROFILE_VIEW', 'PROFILE_UPDATE', 'PASSWORD_CHANGE',
    'AUTH_MANAGEMENT', 'AUTH_LOGIN', 'AUTH_REGISTER', 'AUTH_LOGOUT', 'AUTH_ME'
);
