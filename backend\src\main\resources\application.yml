spring:
  application:
    name: dynamic-permission-system

  # 数据库配置
  datasource:
    url: *******************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop     # 每次启动时重新创建表结构
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect  # MySQL方言
        format_sql: true
        use_sql_comments: true
    open-in-view: false

  # H2控制台配置（开发环境）
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

  # Jackson配置
  jackson:
    default-property-inclusion: non_null
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false

  # 数据审计配置
  data:
    jpa:
      repositories:
        enabled: true

# Swagger/OpenAPI 配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
  show-actuator: false
  group-configs:
    - group: 'permission-system'
      display-name: '权限管理系统'
      paths-to-match: '/api/**'

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# 日志配置
logging:
  level:
    zh.backend: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 权限系统配置
permission:
  # JWT配置
  jwt:
    secret: mySecretKey123456789012345678901234567890123456789012345678901234567890  # 生产环境请使用更安全的密钥，至少64字节
    expiration: 86400000  # 24小时（毫秒）
    refresh-expiration: 604800000  # 7天（毫秒）
    header: Authorization
    prefix: "Bearer "

  # 权限缓存配置
  cache:
    enabled: true
    ttl: 3600  # 缓存时间（秒）

  # 安全配置
  security:
    # 不需要认证的路径
    permit-all-paths:
      - /api/auth/login
      - /api/auth/register
      - /api/auth/refresh
      - /h2-console/**
      - /actuator/health
      - /swagger-ui/**
      - /v3/api-docs/**

    # 需要管理员权限的路径
    admin-paths:
      - /api/admin/**
      - /api/users/**
      - /api/roles/**
      - /api/permissions/**

  # 默认权限配置
  default:
    # 新用户默认角色
    user-role: USER
    # 超级管理员角色
    super-admin-role: SUPER_ADMIN
    # 系统管理员角色
    admin-role: SYSTEM_ADMIN

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
