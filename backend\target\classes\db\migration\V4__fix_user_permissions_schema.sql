-- 修复用户权限表结构 - 添加BaseEntity字段
-- 版本: 4.0
-- 更新时间: 2024-12-19
-- 说明: 为user_permissions表添加缺失的BaseEntity字段

-- 为user_permissions表添加BaseEntity字段
ALTER TABLE user_permissions 
ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN created_by BIGINT,
ADD COLUMN updated_by BIGINT;

-- 为新添加的字段创建索引
CREATE INDEX idx_user_permissions_created_at ON user_permissions(created_at);
CREATE INDEX idx_user_permissions_updated_at ON user_permissions(updated_at);

-- 更新现有记录的created_at字段，使用granted_at的值
UPDATE user_permissions 
SET created_at = granted_at 
WHERE created_at IS NULL;

-- 更新现有记录的created_by字段，使用granted_by的值
UPDATE user_permissions 
SET created_by = granted_by 
WHER<PERSON> created_by IS NULL AND granted_by IS NOT NULL;
