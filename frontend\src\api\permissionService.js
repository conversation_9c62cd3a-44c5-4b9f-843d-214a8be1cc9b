import api from './axios'

const API_URL = '/api/permissions'

/**
 * 权限管理API服务
 */
export const permissionService = {
  /**
   * 获取权限列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getPermissions(params = {}) {
    const response = await api.get(API_URL, { params })
    return response.data
  },

  /**
   * 获取权限树形结构
   * @returns {Promise}
   */
  async getPermissionTree() {
    const response = await api.get(`${API_URL}/tree`)
    return response.data
  },

  /**
   * 根据ID获取权限详情
   * @param {number} id - 权限ID
   * @returns {Promise}
   */
  async getPermissionById(id) {
    const response = await api.get(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 创建权限
   * @param {Object} permissionData - 权限数据
   * @returns {Promise}
   */
  async createPermission(permissionData) {
    const response = await api.post(API_URL, permissionData)
    return response.data
  },

  /**
   * 更新权限
   * @param {number} id - 权限ID
   * @param {Object} permissionData - 权限数据
   * @returns {Promise}
   */
  async updatePermission(id, permissionData) {
    const response = await api.put(`${API_URL}/${id}`, permissionData)
    return response.data
  },

  /**
   * 删除权限
   * @param {number} id - 权限ID
   * @returns {Promise}
   */
  async deletePermission(id) {
    const response = await api.delete(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 批量删除权限
   * @param {Array} ids - 权限ID数组
   * @returns {Promise}
   */
  async deletePermissions(ids) {
    const response = await api.delete(`${API_URL}/batch`, { data: { ids } })
    return response.data
  },

  /**
   * 获取权限统计信息
   * @returns {Promise}
   */
  async getPermissionStats() {
    const response = await api.get(`${API_URL}/stats`)
    return response.data
  },

  /**
   * 检查权限编码是否存在
   * @param {string} code - 权限编码
   * @param {number} excludeId - 排除的权限ID（用于编辑时检查）
   * @returns {Promise}
   */
  async checkPermissionCode(code, excludeId = null) {
    const params = { code }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await api.get(`${API_URL}/check-code`, { params })
    return response.data
  },

  /**
   * 获取用户权限
   * @param {number} userId - 用户ID
   * @returns {Promise}
   */
  async getUserPermissions(userId) {
    const response = await api.get(`${API_URL}/user/${userId}`)
    return response.data
  },

  /**
   * 为用户分配权限
   * @param {number} userId - 用户ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async assignPermissionsToUser(userId, permissionIds) {
    const response = await api.post(`${API_URL}/user/${userId}/assign`, { permissionIds })
    return response.data
  },

  /**
   * 撤销用户权限
   * @param {number} userId - 用户ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async revokePermissionsFromUser(userId, permissionIds) {
    const response = await api.post(`${API_URL}/user/${userId}/revoke`, { permissionIds })
    return response.data
  },

  /**
   * 清除权限缓存
   * @returns {Promise}
   */
  async clearCache() {
    const response = await api.post(`${API_URL}/clear-cache`)
    return response.data
  },

  /**
   * 检查权限编码是否存在
   * @param {string} code - 权限编码
   * @returns {Promise}
   */
  async checkCodeExists(code) {
    const response = await api.get(`${API_URL}/exists`, { params: { code } })
    return response.data
  }
}

export default permissionService
