package zh.backend.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zh.backend.entity.Permission;
import zh.backend.entity.Role;
import zh.backend.entity.User;
import zh.backend.repository.PermissionRepository;
import zh.backend.repository.RoleRepository;
import zh.backend.repository.UserRepository;
import zh.backend.service.RoleService;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 角色服务实现
 */
@Service
@Transactional
public class RoleServiceImpl implements RoleService {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(RoleServiceImpl.class);

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Override
    public Role createRole(Role role) {
        if (existsByCode(role.getCode())) {
            throw new RuntimeException("角色编码已存在: " + role.getCode());
        }
        if (existsByName(role.getName())) {
            throw new RuntimeException("角色名称已存在: " + role.getName());
        }
        return roleRepository.save(role);
    }

    @Override
    public Role updateRole(Long id, Role role) {
        Role existingRole = findById(id);
        
        // 检查编码是否被其他角色使用
        if (!existingRole.getCode().equals(role.getCode()) && existsByCode(role.getCode())) {
            throw new RuntimeException("角色编码已存在: " + role.getCode());
        }
        
        // 检查名称是否被其他角色使用
        if (!existingRole.getName().equals(role.getName()) && existsByName(role.getName())) {
            throw new RuntimeException("角色名称已存在: " + role.getName());
        }
        
        existingRole.setName(role.getName());
        existingRole.setCode(role.getCode());
        existingRole.setDescription(role.getDescription());
        existingRole.setStatus(role.getStatus());
        existingRole.setSortOrder(role.getSortOrder());
        
        return roleRepository.save(existingRole);
    }

    @Override
    public void deleteRole(Long id) {
        Role role = findById(id);
        if (role.getIsSystem()) {
            throw new RuntimeException("系统内置角色不能删除");
        }
        
        // 检查是否有用户使用该角色
        if (!role.getUsers().isEmpty()) {
            throw new RuntimeException("该角色正在被用户使用，无法删除");
        }
        
        roleRepository.deleteById(id);
    }

    @Override
    public Role findById(Long id) {
        return roleRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("角色不存在: " + id));
    }

    @Override
    public Role findByCode(String code) {
        return roleRepository.findByCode(code)
            .orElseThrow(() -> new RuntimeException("角色不存在: " + code));
    }

    @Override
    public List<Role> findAll() {
        return roleRepository.findAll();
    }

    @Override
    public Page<Role> findByKeyword(String keyword, Pageable pageable) {
        return roleRepository.findByKeyword(keyword, pageable);
    }

    @Override
    public List<Role> findActiveRoles() {
        return roleRepository.findActiveRolesOrderBySortOrder();
    }

    @Override
    public void assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        Role role = findById(roleId);
        List<Permission> permissions = permissionRepository.findAllById(permissionIds);
        
        // 清除现有权限
        role.getPermissions().clear();
        
        // 添加新权限
        permissions.forEach(role::addPermission);
        
        roleRepository.save(role);
    }

    @Override
    public void removePermissionsFromRole(Long roleId, List<Long> permissionIds) {
        Role role = findById(roleId);
        List<Permission> permissions = permissionRepository.findAllById(permissionIds);
        
        permissions.forEach(role::removePermission);
        
        roleRepository.save(role);
    }

    @Override
    public List<Permission> getRolePermissions(Long roleId) {
        return permissionRepository.findByRoleId(roleId);
    }

    @Override
    public void assignRolesToUser(Long userId, List<Long> roleIds) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));
        List<Role> roles = roleRepository.findAllById(roleIds);
        
        // 清除现有角色
        user.getRoles().clear();
        
        // 添加新角色
        roles.forEach(user::addRole);
        
        userRepository.save(user);
    }

    @Override
    public void removeRolesFromUser(Long userId, List<Long> roleIds) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));
        List<Role> roles = roleRepository.findAllById(roleIds);
        
        roles.forEach(user::removeRole);
        
        userRepository.save(user);
    }

    @Override
    public List<Role> getUserRoles(Long userId) {
        return roleRepository.findByUserId(userId);
    }

    @Override
    public Set<String> getUserRoleCodes(Long userId) {
        return getUserRoles(userId).stream()
            .map(Role::getCode)
            .collect(Collectors.toSet());
    }

    @Override
    public boolean hasRole(Long userId, String roleCode) {
        Set<String> userRoles = getUserRoleCodes(userId);
        return userRoles.contains(roleCode);
    }

    @Override
    public boolean hasAnyRole(Long userId, String... roleCodes) {
        Set<String> userRoles = getUserRoleCodes(userId);
        for (String roleCode : roleCodes) {
            if (userRoles.contains(roleCode)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean hasAllRoles(Long userId, String... roleCodes) {
        Set<String> userRoles = getUserRoleCodes(userId);
        for (String roleCode : roleCodes) {
            if (!userRoles.contains(roleCode)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean existsByCode(String code) {
        return roleRepository.existsByCode(code);
    }

    @Override
    public boolean existsByName(String name) {
        return roleRepository.existsByName(name);
    }

    @Override
    public void deleteRoles(List<Long> ids) {
        List<Role> roles = roleRepository.findAllById(ids);
        roles.forEach(role -> {
            if (role.getIsSystem()) {
                throw new RuntimeException("系统内置角色不能删除: " + role.getCode());
            }
            if (!role.getUsers().isEmpty()) {
                throw new RuntimeException("角色正在被用户使用，无法删除: " + role.getName());
            }
        });
        roleRepository.deleteAllById(ids);
    }

    @Override
    public void copyRolePermissions(Long sourceRoleId, Long targetRoleId) {
        Role sourceRole = findById(sourceRoleId);
        Role targetRole = findById(targetRoleId);
        
        // 复制权限
        targetRole.getPermissions().clear();
        sourceRole.getPermissions().forEach(targetRole::addPermission);
        
        roleRepository.save(targetRole);
    }

    public RoleStatistics getRoleStatistics() {
        log.debug("Entering getRoleStatistics service method...");
        try {
            long totalRoles = roleRepository.count();
            log.debug("Total roles: {}", totalRoles);
            long activeRoles = roleRepository.countByStatus(Role.RoleStatus.ACTIVE);
            log.debug("Active roles: {}", activeRoles);
            // 注意: findByIsSystemTrue 可能返回 List，处理潜在的 null（虽然在 JPA 中不太可能）
            List<Role> systemRoleList = roleRepository.findByIsSystemTrue();
            long systemRoles = (systemRoleList != null) ? systemRoleList.size() : 0;
            log.debug("System roles: {}", systemRoles);
            long customRoles = totalRoles - systemRoles;
            log.debug("Custom roles: {}", customRoles);

            return new RoleStatistics(totalRoles, activeRoles, systemRoles, customRoles);
        } catch (Exception e) {
            log.error("Error calculating role statistics", e);
            throw e; // 重新抛出以便上游或默认 Spring 处理程序处理
        }
    }
}
