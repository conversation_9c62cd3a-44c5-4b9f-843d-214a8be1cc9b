import api from './axios'

const API_URL = '/api/roles'

/**
 * 角色管理API服务
 */
export const roleService = {
  /**
   * 获取角色列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getRoles(params = {}) {
    const response = await api.get(API_URL, { params })
    return response.data
  },

  /**
   * 根据ID获取角色详情
   * @param {number} id - 角色ID
   * @returns {Promise}
   */
  async getRoleById(id) {
    const response = await api.get(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 创建角色
   * @param {Object} roleData - 角色数据
   * @returns {Promise}
   */
  async createRole(roleData) {
    const response = await api.post(API_URL, roleData)
    return response.data
  },

  /**
   * 更新角色
   * @param {number} id - 角色ID
   * @param {Object} roleData - 角色数据
   * @returns {Promise}
   */
  async updateRole(id, roleData) {
    const response = await api.put(`${API_URL}/${id}`, roleData)
    return response.data
  },

  /**
   * 删除角色
   * @param {number} id - 角色ID
   * @returns {Promise}
   */
  async deleteRole(id) {
    const response = await api.delete(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 批量删除角色
   * @param {Array} ids - 角色ID数组
   * @returns {Promise}
   */
  async deleteRoles(ids) {
    const response = await api.delete(`${API_URL}/batch`, { data: { ids } })
    return response.data
  },

  /**
   * 获取角色权限
   * @param {number} roleId - 角色ID
   * @returns {Promise}
   */
  async getRolePermissions(roleId) {
    const response = await api.get(`${API_URL}/${roleId}/permissions`)
    return response.data
  },

  /**
   * 为角色分配权限
   * @param {number} roleId - 角色ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async assignPermissionsToRole(roleId, permissionIds) {
    const response = await api.post(`${API_URL}/${roleId}/permissions`, { permissionIds })
    return response.data
  },

  /**
   * 为角色分配权限（别名方法，兼容新的调用方式）
   * @param {number} roleId - 角色ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async assignPermissions(roleId, permissionIds) {
    return this.assignPermissionsToRole(roleId, permissionIds)
  },

  /**
   * 从角色中移除权限
   * @param {number} roleId - 角色ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async removePermissions(roleId, permissionIds) {
    const response = await api.delete(`${API_URL}/${roleId}/permissions`, { data: { permissionIds } })
    return response.data
  },

  /**
   * 获取角色用户
   * @param {number} roleId - 角色ID
   * @returns {Promise}
   */
  async getRoleUsers(roleId) {
    const response = await api.get(`${API_URL}/${roleId}/users`)
    return response.data
  },

  /**
   * 为角色分配用户
   * @param {number} roleId - 角色ID
   * @param {Array} userIds - 用户ID数组
   * @returns {Promise}
   */
  async assignUsersToRole(roleId, userIds) {
    const response = await api.post(`${API_URL}/${roleId}/users`, { userIds })
    return response.data
  },

  /**
   * 从角色中移除用户
   * @param {number} roleId - 角色ID
   * @param {Array} userIds - 用户ID数组
   * @returns {Promise}
   */
  async removeUsersFromRole(roleId, userIds) {
    const response = await api.delete(`${API_URL}/${roleId}/users`, { data: { userIds } })
    return response.data
  },

  /**
   * 获取角色统计信息
   * @returns {Promise}
   */
  async getRoleStats() {
    const response = await api.get(`${API_URL}/stats`)
    return response.data
  },

  /**
   * 检查角色编码是否存在
   * @param {string} code - 角色编码
   * @param {number} excludeId - 排除的角色ID（用于编辑时检查）
   * @returns {Promise}
   */
  async checkRoleCode(code, excludeId = null) {
    const params = { code }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await api.get(`${API_URL}/check-code`, { params })
    return response.data
  },

  /**
   * 更新角色状态
   * @param {number} id - 角色ID
   * @param {string} status - 状态
   * @returns {Promise}
   */
  async updateRoleStatus(id, status) {
    const response = await api.patch(`${API_URL}/${id}/status`, { status })
    return response.data
  }
}

export default roleService
